# .github/workflows/release.yml

name: Release

permissions:
  contents: write

on:
  push:
    tags:
      - v[0-9]+.[0-9]+.[0-9]+ # Matches version tags like v1.0.0, v2.3.4, etc.

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set node
        uses: actions/setup-node@v4
        with:
          registry-url: https://registry.npmjs.org/
          node-version: lts/*

      - run: npx changelogithub # or changelogithub@0.12 to ensure a stable result
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
