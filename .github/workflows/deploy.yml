name: Deploy to Environment

on:
  pull_request:
    branches:
      - main
      - develop
    types:
      - closed

env:
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to environment
        uses: appleboy/ssh-action@v1
        if: github.event.pull_request.merged
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          port: ${{ secrets.SSH_PORT }}
          script: BRANCH_NAME=${{ BRANCH_NAME }} cd deploy/genie-portal && ./deploy.sh
