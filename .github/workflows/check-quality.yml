# Based on https://github.com/actions/starter-workflows/blob/main/ci/node.js.yml
name: Check Quality

on:
  pull_request:
    branches:
      - main
      - develop

jobs:
  check-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Enable Corepack
        run: corepack enable
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --immutable

      - name: Run ESLint
        run: yarn lint:check

      - name: Check formatting with Prettier
        run: yarn prettier:check

      # Triggering SonarQube analysis as results of it are required by Quality Gate check.
      - name: Run SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@v5.2.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
        with:
          args: >
            -Dsonar.projectKey=genie-portal
            -Dsonar.projectVersion=latest
            -Dsonar.sources=.
            -Dsonar.exclusions=node_module/**,config/**,scripts/**,src/api/** \

      # Check the Quality Gate status.
      - name: SonarQube Quality Gate check
        id: sonarqube-quality-gate-check
        uses: sonarsource/sonarqube-quality-gate-action@master
        with:
          pollingTimeoutSec: 600
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Run build
        run: NODE_OPTIONS="--max-old-space-size=8192" yarn build
