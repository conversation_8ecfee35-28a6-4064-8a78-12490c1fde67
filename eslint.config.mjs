import pluginImport from 'eslint-plugin-import'
import reactPlugin from 'eslint-plugin-react'
import jestPlugin from 'eslint-plugin-jest'

export default [
  {
    plugins: {
      import: pluginImport,
      react: reactPlugin,
      jest: jestPlugin,
    },
    rules: {
      'import/no-cycle': ['error', { maxDepth: Infinity }],
      'import/no-self-import': 'error',
      'import/no-useless-path-segments': ['error', { noUselessIndex: true }],
      'react/jsx-uses-react': 'warn',
      'jest/no-disabled-tests': 'warn',
    },
    settings: {
      'import/resolver': {
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx'],
        },
      },
    },
  },
]
