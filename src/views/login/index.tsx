import { ArrowRightOutlined } from '@ant-design/icons'
import {
  App,
  Button,
  Flex,
  Form,
  Input,
  QRCode,
  QRCodeProps,
  Space,
} from 'antd'
import PasswordFormItem, {
  PasswordFormItemRefProps,
} from 'components/password-form-item'
import { useExNavigate } from 'hooks/use-ex-navigate'
import { isHydrated } from 'mobx-persist-store'
import { observer } from 'mobx-react'
import React, { useEffect, useRef, useState } from 'react'
import { LoginUserAction } from 'stores/models/user'
import userStore from 'stores/user'
import { getMessageFromError } from 'utils/common'
import { LoginHeader } from './components/header'

import { GridBackground } from './components/grid-bg'
import './index.css'

const layout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 },
}

const tailLayout = {
  wrapperCol: { span: 24 },
}

enum LoginStepType {
  LOGIN,
  TWO_FACTOR_AUTH,
  RESET_PASSWORD,
  SETUP_TWO_FACTOR_AUTH,
}

const QR_CODE_EXPIRED_TIME = 10 * 60000

const Login: React.FC = observer(() => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [loginStep, setLoginStep] = useState(LoginStepType.LOGIN)
  const [qrUrl, setQrUrl] = useState('')
  const [qrStatus, setQrStatus] = useState<QRCodeProps['status']>('expired')
  // TODO: passwordFormItemRef does not seem to be used anywhere, remove?
  const passwordFormItemRef = useRef<PasswordFormItemRefProps>(null)
  // END TODO
  const navigate = useExNavigate()

  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      const otp = values.otp?.toString() ?? ''
      const loginUser = await userStore.login(
        values.email,
        values.password,
        otp,
      )

      if (loginUser?.action === LoginUserAction.ChangePassword) {
        setLoginStep(LoginStepType.RESET_PASSWORD)
        message.warning('Please change your password')
        return
      } else if (loginUser?.action === LoginUserAction.Setup2FA) {
        setLoginStep(LoginStepType.SETUP_TWO_FACTOR_AUTH)
        refreshQR()
        message.warning('Please setup your 2FA!')
        return
      } else if (loginUser?.action === LoginUserAction.Validate2FA) {
        setLoginStep(LoginStepType.TWO_FACTOR_AUTH)
        message.warning('Please validate your 2FA')
        return
      }
      message.success('Login successful!')
      navigate('/portal/dashboard')
    } catch (error: any) {
      message.error(getMessageFromError(error))
    } finally {
      setLoading(false)
    }
  }

  const onSetup2FA = async () => {
    if (!userStore.loginUser?.sessionID || !userStore.loginUser?.id) {
      return
    }
    try {
      const password: string = form.getFieldValue('password')
      const authCode: string = form.getFieldValue('authCode')
      await userStore.twoFactorAuthSetupSession(
        userStore.loginUser.sessionID,
        userStore.loginUser.id,
        authCode,
      )
      await onFinish({
        email: userStore.loginUser.email,
        password,
        otp: authCode,
      })
      message.success('2FA Enabled Successfully!')
    } catch (error) {
      message.error(getMessageFromError(error))
    }
  }

  const onResetPassword = async () => {
    if (!userStore.loginUser?.sessionID || !userStore.loginUser?.id) {
      return
    }
    try {
      const password = form.getFieldValue('password')
      const email = form.getFieldValue('email')
      await userStore.updatePasswordSession(
        userStore.loginUser.sessionID,
        userStore.loginUser.id,
        password,
      )
      await onFinish({ email, password })
    } catch (error) {
      message.error(getMessageFromError(error))
    }
  }

  const refreshQR = (action: 'auto' | 'manual' = 'auto') => {
    if (!userStore.loginUser?.sessionID || !userStore.loginUser?.id) {
      return
    }
    setQrStatus('loading')
    userStore
      .getTwoFactorAuthQRCodeSession(
        userStore.loginUser?.sessionID,
        userStore.loginUser?.id,
      )
      .then(response => {
        if (typeof response.data === 'string') {
          setQrUrl(response.data)
          setQrStatus('active')
          if (action === 'manual') {
            setTimeout(() => {
              setQrStatus('expired')
            }, QR_CODE_EXPIRED_TIME)
          }
        }
      })
      .catch(error => {
        message.error(getMessageFromError(error))
      })
  }

  const isDisplay = (step: LoginStepType) => {
    return { display: loginStep === step ? undefined : 'none' }
  }

  useEffect(() => {
    if (isHydrated(userStore) && userStore.loginUser) {
      navigate('/portal/dashboard')
    }
  }, [navigate])

  return (
    <div className="login-wrapper">
      <div className="login-box">
        <div className="login-form">
          {loginStep !== LoginStepType.SETUP_TWO_FACTOR_AUTH && <LoginHeader />}
          <div
            // setup 2fa have different width
            className={`fields ${loginStep === LoginStepType.SETUP_TWO_FACTOR_AUTH ? 'setup-2fa' : ''}`}
          >
            <Form
              {...layout}
              form={form}
              name="control-hooks"
              onFinish={onFinish}
              style={{ maxWidth: 600, margin: '0 auto' }}
            >
              <Space
                direction="vertical"
                className="field-group"
                style={isDisplay(LoginStepType.LOGIN)}
              >
                <Form.Item
                  name="email"
                  label="Email"
                  rules={[
                    { required: true, message: 'Please enter your email' },
                  ]}
                >
                  <Input placeholder="Enter your email" />
                </Form.Item>
                {[
                  LoginStepType.LOGIN,
                  LoginStepType.TWO_FACTOR_AUTH,
                  LoginStepType.SETUP_TWO_FACTOR_AUTH,
                ].includes(loginStep) && (
                  <Form.Item
                    name="password"
                    label="Password"
                    rules={[
                      {
                        required: true,
                        message: 'Please enter your password',
                      },
                    ]}
                    style={isDisplay(LoginStepType.LOGIN)}
                  >
                    <Input.Password
                      placeholder="Enter your password"
                      type="password"
                    />
                  </Form.Item>
                )}
              </Space>
              {LoginStepType.RESET_PASSWORD === loginStep && (
                <div className="field-group">
                  <PasswordFormItem
                    ref={passwordFormItemRef}
                    form={form}
                    validateFirst
                  />
                </div>
              )}
              {LoginStepType.SETUP_TWO_FACTOR_AUTH === loginStep && (
                <Form.Item>
                  <div className="setup-2fa-content">
                    <GridBackground />
                    <QRCode
                      className="qr-code"
                      value={qrUrl}
                      status={qrStatus}
                      onRefresh={() => refreshQR('manual')}
                      size={280}
                      bgColor="rgba(255, 255, 255, 0.5)"
                    />
                    <div className="setup-instructions">
                      <h3>Please follow below instructions:</h3>
                      <p>1. Scan with 2FA&nbsp;&nbsp;Authenticator</p>
                      <p>
                        2. Enter the 6-digits code provided by the authenticator
                        app
                      </p>
                      <Form.Item
                        name="authCode"
                        rules={[
                          {
                            required: true,
                            message: 'Please enter your authentication code',
                          },
                        ]}
                      >
                        <Input placeholder="Enter your 2FA code" />
                      </Form.Item>
                      <Flex gap={12}>
                        <Button
                          onClick={() => setLoginStep(LoginStepType.LOGIN)}
                        >
                          Back
                        </Button>
                        <Button
                          color="default"
                          variant="solid"
                          onClick={onSetup2FA}
                        >
                          Setup
                        </Button>
                      </Flex>
                    </div>
                  </div>
                </Form.Item>
              )}
              {LoginStepType.TWO_FACTOR_AUTH === loginStep && (
                <Form.Item
                  name="otp"
                  className="two-factor-auth-form-item"
                  label="Please enter your 2FA code"
                  rules={[
                    {
                      required: loginStep === LoginStepType.TWO_FACTOR_AUTH,
                      message: 'Please enter your authentication code',
                    },
                  ]}
                >
                  <Input
                    placeholder="Enter your 2FA Code"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}
              {loginStep !== LoginStepType.SETUP_TWO_FACTOR_AUTH && (
                <Form.Item {...tailLayout}>
                  <Flex gap={12} vertical>
                    {LoginStepType.LOGIN === loginStep && (
                      <div className="circle-button-wrapper">
                        <Button
                          color="default"
                          variant="solid"
                          shape="circle"
                          htmlType="submit"
                          className="login-btn"
                          loading={loading}
                          icon={<ArrowRightOutlined />}
                        />
                      </div>
                    )}
                    {LoginStepType.RESET_PASSWORD === loginStep && (
                      <Flex justify="space-between" gap={12}>
                        <Button
                          style={{ flex: 1 }}
                          onClick={() => setLoginStep(LoginStepType.LOGIN)}
                          size="large"
                        >
                          Back
                        </Button>
                        <Button
                          color="default"
                          variant="solid"
                          className="login-btn"
                          loading={loading}
                          onClick={onResetPassword}
                          size="large"
                          style={{ flex: 1 }}
                        >
                          Reset
                        </Button>
                      </Flex>
                    )}
                    {LoginStepType.TWO_FACTOR_AUTH === loginStep && (
                      <Flex justify="space-between" gap={12}>
                        <Button
                          style={{ flex: 1 }}
                          onClick={() => setLoginStep(LoginStepType.LOGIN)}
                          size="large"
                        >
                          Back
                        </Button>
                        <Button
                          color="default"
                          variant="solid"
                          htmlType="submit"
                          className="login-btn"
                          loading={loading}
                          size="large"
                          style={{ flex: 1 }}
                        >
                          Login
                        </Button>
                      </Flex>
                    )}
                  </Flex>
                </Form.Item>
              )}
            </Form>
          </div>
        </div>
      </div>
    </div>
  )
})

export default Login
