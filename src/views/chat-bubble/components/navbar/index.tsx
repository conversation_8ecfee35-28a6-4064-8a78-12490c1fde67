import { memo, useState } from 'react'
import cls from 'classnames'
import { Button, Modal, Tooltip } from 'antd'
import {
  QuestionCircleOutlined,
  MinusOutlined,
  CloseOutlined,
} from '@ant-design/icons'

import styles from './index.scss'
import { useMessageWorker } from 'hooks/use-message-worker'

type NavbarType = {
  fqaBtnText?: string
  fqaContent?: string
}

const Navbar = (props: NavbarType) => {
  const { fqaBtnText, fqaContent } = props
  const [isFqaModalVisible, setIsFqaModalVisible] = useState(false)
  const messageWorker = useMessageWorker()

  const handleMiniClick = () => {
    messageWorker.postActionMessage({ type: 'mini' })
  }
  const handleCloseClick = () => {
    messageWorker.postActionMessage({ type: 'close' })
    location.reload()
  }

  return (
    <div className={cls(styles.navbarWrapper, 'chat-bubble-bar-wrapper')}>
      <Tooltip placement="bottom" title={fqaBtnText ?? ''}>
        <QuestionCircleOutlined
          className={cls(
            styles.navbarIcon,
            styles.navbarIconFqa,
            'chat-bubble-bar-help-icon',
          )}
          onClick={() => setIsFqaModalVisible(true)}
        />
      </Tooltip>

      <Button
        className={cls(styles.navbarIcon, 'chat-bubble-bar-mini-icon')}
        type="text"
        icon={<MinusOutlined />}
        onClick={handleMiniClick}
      />

      <Button
        className={cls(styles.navbarIcon, 'chat-bubble-bar-close-icon')}
        type="text"
        icon={<CloseOutlined />}
        onClick={handleCloseClick}
      />

      <Modal
        closable={false}
        maskClosable={false}
        open={isFqaModalVisible}
        footer={
          <Button type="primary" onClick={() => setIsFqaModalVisible(false)}>
            Got it
          </Button>
        }
      >
        <div dangerouslySetInnerHTML={{ __html: fqaContent ?? '' }} />
      </Modal>
    </div>
  )
}

export default memo(Navbar)
