import classNames from 'classnames'
import styles from './index.module.scss'

/**
 * Wrapper for card list UI
 * see agent list and market place for examples
 */

interface CardListLayoutProps {
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export function CardListLayout({
  children,
  className,
  style,
}: Readonly<CardListLayoutProps>) {
  return (
    <div className={classNames(styles.wrapper, className)} style={style}>
      {children}
    </div>
  )
}

export default CardListLayout
