import MainLayout from 'layouts/portal/page-main-layout'
import React, { useState } from 'react'
import { deepCopy } from 'utils/common'
import ApiKeyModal, { ApiKeyModalProps } from './api-key-modal'
import ApiKeyTable, { ActionType, ApiKeyTableRef } from './api-key-table'
import styles from './index.scss'

const DEFAULT_API_KEY_DATA: ApiKeyModalProps['data'] = {
  name: '',
  clientId: '',
  secretKey: '',
  lastUsedAt: '',
  createdAt: '',
  updatedAt: '',
}

const QuestionPage: React.FC = () => {
  const [isApiKeyModalOpen, setIsApiKeyModalOpen] = useState(false)
  const [apiKey, setApiKey] =
    useState<ApiKeyModalProps['data']>(DEFAULT_API_KEY_DATA)
  const apiKeyTableRef = React.createRef<ApiKeyTableRef>()

  const handleCreateButtonClick = () => {
    setApiKey(deepCopy({ ...DEFAULT_API_KEY_DATA }))
    setIsApiKeyModalOpen(true)
  }

  return (
    <MainLayout
      title="API Keys"
      actions={[
        {
          text: 'Create API Key',
          isPrimary: true,
          onClick: handleCreateButtonClick,
        },
      ]}
      noBack
      noHeaderBottomLine
    >
      <div className={styles.apiKeyDescription}>
        Manage your API keys. Remember to keep your API keys safe to prevent
        unauthorized access.
      </div>
      <ApiKeyTable
        ref={apiKeyTableRef}
        onAction={(type, data) => {
          if (type === ActionType.Edit) {
            setApiKey({
              id: data.id,
              name: data.name,
              clientId: data.clientID,
              secretKey: data.secret,
              lastUsedAt: data.latestUsageTime,
              createdAt: data.createdAt,
              updatedAt: data.updatedAt,
            })
            setIsApiKeyModalOpen(true)
          }
        }}
      />
      <ApiKeyModal
        open={isApiKeyModalOpen}
        title={apiKey.id ? 'Edit API Key' : 'Create API Key'}
        okText="Save"
        data={apiKey}
        onFinish={() => {
          apiKeyTableRef.current?.refresh()
          setIsApiKeyModalOpen(false)
        }}
        onCancel={() => setIsApiKeyModalOpen(false)}
      />
    </MainLayout>
  )
}

export default QuestionPage
