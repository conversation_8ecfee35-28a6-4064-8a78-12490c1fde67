import { CopyOutlined, EditOutlined } from '@ant-design/icons'
import { App, Button, Flex, Form, Input, Modal } from 'antd'
import { v1ApiKeyCreate, v1ApiKeyUpdate } from 'api/Api'
import { ResponsesAPIKeyResponse } from 'api/data-contracts'
import { ReactNode, useEffect, useMemo, useState } from 'react'
import { getMessageFromError } from 'utils/common'
import styles from './api-key-modal.scss'

export interface ApiKeyModalProps {
  data: {
    id?: number
    name: string
    clientId: string
    secretKey: string
    createdAt?: string
    updatedAt?: string
    lastUsedAt?: string
  }
  title?: ReactNode
  okText?: string
  open?: boolean
  onFinish?: (apiKey?: ResponsesAPIKeyResponse) => void
  onCancel?: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const ApiKeyModal: React.FC<ApiKeyModalProps> = props => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [apiKey, setApiKey] = useState<ResponsesAPIKeyResponse>()
  const isNewKeyCreated = useMemo(
    () => props.data.id === undefined && apiKey?.secret,
    [props.data.id, apiKey],
  )

  useEffect(() => {
    if (props.open) {
      setApiKey(undefined)
      form.setFieldsValue({ ...props.data })
    }
  }, [props.open, form, props.data])

  const handleSave = async (values: ApiKeyModalProps['data']) => {
    setConfirmLoading(true)
    try {
      if (props.data.id == undefined) {
        const response = await v1ApiKeyCreate({
          name: values.name,
        })
        const data = response.data
        message.success('API key is created successfully!')
        return data
      } else {
        const response = await v1ApiKeyUpdate(props.data.id, {
          name: values.name,
        })
        const data = response.data
        message.success('API key is updated successfully!')
        return data
      }
    } catch (error) {
      message.error(getMessageFromError(error))
    } finally {
      setConfirmLoading(false)
    }
  }

  const handleFinish = async (values: ApiKeyModalProps['data']) => {
    const newApiKey = await handleSave(values)
    if (newApiKey === undefined) {
      return
    }
    // If the API key is newly created, set the secret key and show it to the
    // user. Otherwise, close the modal.
    if (props.data.id === undefined) {
      setApiKey(newApiKey)
      return
    }

    props.onFinish?.(newApiKey)
  }

  const handleCancel = (e: React.MouseEvent<HTMLButtonElement>) => {
    form.resetFields()
    props.onCancel?.(e)
  }

  return (
    <Modal
      title={props.title ?? 'API Key'}
      open={props.open}
      okText={isNewKeyCreated ? 'Done' : 'Submit'}
      cancelButtonProps={{
        style: { visibility: isNewKeyCreated ? 'hidden' : 'visible' },
      }}
      onOk={() => (isNewKeyCreated ? props.onFinish?.(apiKey) : form.submit())}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      width={640}
    >
      {isNewKeyCreated ? (
        <Flex gap={24} style={{ padding: '24px 0' }} vertical>
          <div>
            Your new API key has been created.{' '}
            <b>Copy it now, as we will not display it again.</b>
          </div>
          <Flex gap={12}>
            <span className={styles.newApiKeyLabel}>Client ID:</span>
            <Input value={apiKey?.clientID} readOnly />
            <Button
              type="primary"
              onClick={() => {
                navigator.clipboard
                  .writeText(apiKey?.secret ?? '')
                  .then(() => {
                    message.success('Your secret has been copied!')
                  })
                  .catch(e => {
                    message.error('Failed to copy secret!')
                    console.error(e.message)
                  })
              }}
            >
              Copy <CopyOutlined />
            </Button>
          </Flex>
          <Flex gap={12}>
            <span className={styles.newApiKeyLabel}>Secret:</span>
            <Input value={apiKey?.secret} readOnly />
            <Button
              type="primary"
              onClick={() => {
                navigator.clipboard
                  .writeText(apiKey?.secret ?? '')
                  .then(() => {
                    message.success('Your secret has been copied!')
                  })
                  .catch(e => {
                    message.error('Failed to copy secret!')
                    console.error(e.message)
                  })
              }}
            >
              Copy <CopyOutlined />
            </Button>
          </Flex>
        </Flex>
      ) : (
        <Form
          name="basic"
          layout="vertical"
          form={form}
          onFinish={handleFinish}
          autoComplete="off"
          style={{ paddingTop: '24px 0' }}
        >
          <Form.Item
            name="name"
            label="Enter a display name for the key (max 50 characters)"
            rules={[
              { required: true, message: 'Please input a name!' },
              { max: 50, message: 'Name should be less than 50 characters!' },
            ]}
          >
            <Input prefix={<EditOutlined />} placeholder="" />
          </Form.Item>
        </Form>
      )}
    </Modal>
  )
}

export default ApiKeyModal
