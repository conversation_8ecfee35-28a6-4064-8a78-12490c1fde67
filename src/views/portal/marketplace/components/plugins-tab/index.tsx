import { App, Empty, Skeleton } from 'antd'
import { v1PluginsList } from 'api/Api'
import { type ResponsesPluginWithLatestFormsOfAllStatusesResponse } from 'api/data-contracts'
import CustomPagination from 'components/custom-pagination'
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from 'constants/pagination'
import { useDebounceValue } from 'hooks/use-debounce-value'
import { useCallback, useEffect, useState } from 'react'
import CardListLayout from 'views/layouts/card-list'
import { useFilterUrlState } from '../../hooks/use-filter-state'
import PluginCard from './plugin-card'

export function PluginsTab() {
  const { message } = App.useApp()
  const [pluginList, setPluginList] = useState<
    ResponsesPluginWithLatestFormsOfAllStatusesResponse[]
  >([])
  const [loading, setLoading] = useState(false)
  const [{ search, sort, filter }] = useFilterUrlState()
  const debouncedSearch = useDebounceValue(search, 500)
  const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [totalItems, setTotalItems] = useState(0)

  const fetchData = useCallback(
    async (param: { page?: number; limit?: number }) => {
      const { page = currentPage, limit = pageSize } = param
      setLoading(true)
      try {
        const pluginResp = await v1PluginsList({
          page,
          limit,
          search_plugin_name: debouncedSearch,
          sort: JSON.stringify(sort),
          category_ids: filter.category_ids
            ? filter.category_ids.join(',')
            : undefined,
        })

        if (!pluginResp?.data) return

        const { data, pagination } = pluginResp.data
        setPluginList(data)
        setTotalItems(pagination.totalItems)
      } catch (error) {
        message.error(
          `Failed to load plugins: ${error instanceof Error ? error.message : 'Unknown error'}`,
        )
      } finally {
        setLoading(false)
      }
    },
    [currentPage, pageSize, debouncedSearch, sort, message, filter],
  )

  useEffect(() => {
    fetchData({ page: currentPage, limit: pageSize })
  }, [currentPage, pageSize, debouncedSearch, sort, fetchData])

  const handlePageChange = useCallback((page: number, size: number) => {
    // Update the local state immediately for UI feedback
    setCurrentPage(page)
    setPageSize(size)
    // The data will be fetched automatically by the effect when state changes
  }, [])

  const renderContent = () => {
    if (loading) {
      return <Skeleton active />
    }

    if (pluginList.length === 0) {
      return <Empty description="No plugins found" style={{ width: '100%' }} />
    }

    return pluginList.map(item => (
      <PluginCard
        key={item.id}
        data={item}
        onUpdate={() => fetchData({ page: currentPage, limit: pageSize })}
      />
    ))
  }

  return (
    <>
      <CardListLayout
        style={{
          height: 'calc(100vh - 230px)',
        }}
      >
        {renderContent()}
      </CardListLayout>
      <div className="pagination">
        <CustomPagination
          align="center"
          total={totalItems}
          current={currentPage}
          pageSize={pageSize}
          onChange={handlePageChange}
          showSizeChanger
          showTotal={total => `Total ${total} items`}
          disabled={loading}
        />
      </div>
    </>
  )
}

export default PluginsTab
