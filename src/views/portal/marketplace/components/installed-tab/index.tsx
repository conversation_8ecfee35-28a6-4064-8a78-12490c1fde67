import { App, Empty, Skeleton } from 'antd'
import { v1PluginInstalledConfigurationFormPartialUpdate } from 'api/Api'
import CustomPagination from 'components/custom-pagination'
import type { FormItemType } from 'components/new-dynamic-form/types'
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from 'constants/pagination'
import { parseAsInteger, useQueryState } from 'nuqs'
import { useCallback, useState } from 'react'
import { getMessageFromError } from 'utils/common'
import invariant from 'utils/invariant'
import CardListLayout from 'views/layouts/card-list'
import { useFilterUrlState } from '../../hooks/use-filter-state'
import { usePluginInstalledConfigFormDetail } from '../../hooks/use-plugin-installed-configuration-form-detail'
import usePluginInstalledList from '../../hooks/use-plugin-installed-list'
import { ConfigurePluginModal } from '../configure-plugin-modal'
import InstalledPluginCard from '../installed-plugin-card'

export function InstalledTab() {
  const { message } = App.useApp()

  const [isInstallModalOpen, setIsInstallModalOpen] = useState(false)
  const [formStructure, setFormStructure] = useState<FormItemType | null>(null)
  const [formValue, setFormValue] = useState<any>(null)
  const [pluginId, setPluginId] = useQueryState('pluginId', parseAsInteger)
  const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [{ search, sort, filter }] = useFilterUrlState()

  const { result, isLoading, fetchPlugins } = usePluginInstalledList({
    params: {
      page: currentPage,
      limit: pageSize,
      search_plugin_name: search,
      sort: JSON.stringify(sort),
      category_ids: filter.category_ids
        ? filter.category_ids.join(',')
        : undefined,
    },
  })

  const handlePageChange = useCallback(
    (page: number, size: number) => {
      // Save the previous page and size
      const prevPage = currentPage
      const prevSize = pageSize

      // Update the local state immediately for UI feedback
      setCurrentPage(page)
      setPageSize(size)

      fetchPlugins({
        onError: () => {
          message.error(`Failed to load page ${page}`)
          // Revert to the previous page and size on error, setting the state would automatically trigger the refetch
          setCurrentPage(prevPage)
          setPageSize(prevSize)
        },
      })
    },
    [fetchPlugins, message, currentPage, pageSize],
  )

  const { fetchData: fetchPluginFormDetail } =
    usePluginInstalledConfigFormDetail()

  const handleConfigure = (pluginId: number) => {
    setPluginId(pluginId)
    fetchPluginFormDetail({
      params: {
        pluginId,
      },
      onSuccess: formData => {
        setIsInstallModalOpen(true)
        setFormStructure(formData.configurationForm as unknown as FormItemType)
        setFormValue(formData.configurationFormInputs)
      },
      onError: error => {
        message.error(`Failed to load plugin configuration: ${error.message}`)
      },
    })
  }

  const handleConfigureSubmit = async (values: any) => {
    invariant(pluginId, 'Plugin ID is required')
    try {
      await v1PluginInstalledConfigurationFormPartialUpdate(pluginId, {
        inputs: values,
      })
      message.success('Configuration saved successfully')
      setIsInstallModalOpen(false)
      fetchPlugins()
    } catch (error) {
      message.error(
        `Failed to save configuration: ${getMessageFromError(error)}`,
      )
    }
  }

  const handleModalClose = () => {
    setIsInstallModalOpen(false)
    setFormStructure(null)
    setFormValue(null)
    setPluginId(null)
  }

  if (isLoading) {
    return (
      <CardListLayout>
        <Skeleton active />
      </CardListLayout>
    )
  }

  if (!result.success) {
    return (
      <CardListLayout>
        <Empty
          description={getMessageFromError(result.error)}
          style={{ width: '100%' }}
        />
      </CardListLayout>
    )
  }

  if (!result.data.data.length) {
    return (
      <CardListLayout>
        <Empty description="No plugins found" style={{ width: '100%' }} />
      </CardListLayout>
    )
  }

  const { data: plugins, pagination } = result.data

  return (
    <>
      <CardListLayout style={{ height: 'calc(100vh - 230px)' }}>
        {plugins.length === 0 ? (
          <Empty description="No plugins found" style={{ width: '100%' }} />
        ) : (
          plugins.map(plugin => (
            <InstalledPluginCard
              key={plugin.id}
              plugin={plugin}
              onUninstall={fetchPlugins}
              onUpdate={fetchPlugins}
              onConfigure={handleConfigure}
            />
          ))
        )}
      </CardListLayout>
      <div className="pagination">
        <CustomPagination
          total={pagination.totalItems}
          pageSize={pageSize}
          current={currentPage}
          onChange={handlePageChange}
          align="center"
        />
      </div>
      <ConfigurePluginModal
        open={isInstallModalOpen}
        onClose={handleModalClose}
        formStructure={formStructure}
        formValue={formValue}
        onSubmit={handleConfigureSubmit}
      />
    </>
  )
}

export default InstalledTab
