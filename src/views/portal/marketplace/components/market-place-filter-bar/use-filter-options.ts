import { v1PluginCategoriesList } from 'api/Api'
import type { ResponsesPluginCategoryResponse } from 'api/data-contracts'
import { useEffect, useState } from 'react'
import type { FilterOptionsGroup } from 'views/components/filter-bar/types'

export function useFilterOptions() {
  const [categories, setCategories] = useState<
    ResponsesPluginCategoryResponse[]
  >([])
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await v1PluginCategoriesList({
          page: 1,
          limit: 100,
        })
        if (response?.data?.data) {
          setCategories(response.data.data)
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
      }
    }

    fetchCategories()
  }, [])

  return [
    {
      key: 'category_ids',
      label: 'Category',
      options:
        categories?.map(category => ({
          label: category.name,
          value: category.id.toString(),
        })) || [],
    },
  ] satisfies FilterOptionsGroup[]
}
