// Define the data type for the Marketplace context

import type { OutletContext } from 'hooks/use-outlet-context-value'
import useOutletContextValue from 'hooks/use-outlet-context-value'

export type MarketplaceOutletContext = OutletContext<null>

const useMarketplaceOutletContextValue = (): MarketplaceOutletContext => {
  return useOutletContextValue<null>(null)
}

export default useMarketplaceOutletContextValue
