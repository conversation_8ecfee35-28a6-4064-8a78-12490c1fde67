import { v1PluginMarketplaceList } from 'api/Api'
import type { ResponsesGetPluginMarketplaceWithLatestPluginFormResponse } from 'api/data-contracts'
import useDeepCompareEffect from 'hooks/use-deep-compare-effect'
import { useCallback, useRef, useState } from 'react'
import type { WithPaginationResponse } from 'types/common'
import { Result } from 'types/utils'
import { FetchOptions, PluginListParams } from './types'

type UsePluginMarketplaceListParams = PluginListParams

type PluginMarketplaceListData =
  WithPaginationResponse<ResponsesGetPluginMarketplaceWithLatestPluginFormResponse>

interface UsePluginMarketplaceListReturn {
  result: Result<PluginMarketplaceListData>
  isLoading: boolean
  fetchPlugins: (
    options?: FetchOptions<
      UsePluginMarketplaceListParams,
      PluginMarketplaceListData
    >,
  ) => Promise<void>
}

const EMPTY_RESULT: Result<PluginMarketplaceListData> = {
  success: true,
  data: {
    data: [],
    pagination: { totalItems: 0, currentPage: 0, perPage: 0 },
  },
}

/**
 * @description usePluginMarketplaceList is a hook that fetches the plugin marketplace list
 * @param options - FetchOptions<UsePluginMarketplaceListParams>, when params changed, effect will be triggered and refetch the data
 * @returns UsePluginMarketplaceListReturn
 */
export const usePluginMarketplaceList = (
  options: FetchOptions<UsePluginMarketplaceListParams> = {},
): UsePluginMarketplaceListReturn => {
  const [isLoading, setIsLoading] = useState(false)
  const abortControllerRef = useRef<AbortController | null>(null)

  const [result, setResult] =
    useState<Result<PluginMarketplaceListData>>(EMPTY_RESULT)

  // manually trigger fetch, usually not used, because effect will run when params changed
  const fetchPlugins = useCallback(
    async (
      fetchOptions?: FetchOptions<
        UsePluginMarketplaceListParams,
        PluginMarketplaceListData
      >,
    ) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const currentController = new AbortController()
      abortControllerRef.current = currentController

      try {
        setIsLoading(true)
        setResult((prev: Result<PluginMarketplaceListData>) =>
          prev.success
            ? { ...prev, data: { ...prev.data, data: [] } }
            : EMPTY_RESULT,
        )

        const { page, limit, sort, ...restParams } = fetchOptions?.params ?? {}

        const response = await v1PluginMarketplaceList(
          {
            ...options.params,
            page,
            limit,
            sort,
            ...restParams,
          },
          {
            signal: currentController.signal,
          },
        )

        if (currentController === abortControllerRef.current) {
          const { data, pagination } = response.data
          setResult({
            success: true,
            data: {
              data,
              pagination,
            },
          })
          const resultData = {
            data,
            pagination,
          }
          fetchOptions?.onSuccess?.(resultData)
        }
      } catch (err) {
        if (
          // current request throws error and is not aborted
          currentController === abortControllerRef.current &&
          !currentController.signal.aborted
        ) {
          setResult({
            success: false,
            error: err as Error,
          })
          fetchOptions?.onError?.(err as Error)
        }
      } finally {
        if (currentController === abortControllerRef.current) {
          setIsLoading(false)
        }
      }
    },
    [result],
  )

  useDeepCompareEffect(() => {
    if (options.params) {
      fetchPlugins({ params: options.params })
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [options.params])

  return {
    result,
    isLoading,
    fetchPlugins,
  }
}

export default usePluginMarketplaceList
