import { v1PluginInstalledList } from 'api/Api'
import type { ResponsesGetPluginMarketplaceWithLatestPluginFormResponse } from 'api/data-contracts'
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from 'constants/pagination'
import useDeepCompareEffect from 'hooks/use-deep-compare-effect'
import { useCallback, useRef, useState } from 'react'
import type { WithPaginationResponse } from 'types/common'
import { Result } from 'types/utils'
import { FetchOptions, PluginListParams } from './types'

type UsePluginInstalledListParams = PluginListParams

type PluginInstalledListData =
  WithPaginationResponse<ResponsesGetPluginMarketplaceWithLatestPluginFormResponse>

interface UsePluginInstalledListReturn {
  result: Result<PluginInstalledListData>
  isLoading: boolean
  fetchPlugins: (
    options?: FetchOptions<
      UsePluginInstalledListParams,
      PluginInstalledListData
    >,
  ) => Promise<void>
}

const EMPTY_RESULT: Result<PluginInstalledListData> = {
  success: true,
  data: {
    data: [],
    pagination: { totalItems: 0, currentPage: 0, perPage: 0 },
  },
}

// @kenan extract reusable hooks for paginated list fetch
export const usePluginInstalledList = (
  options: FetchOptions<UsePluginInstalledListParams> = {},
): UsePluginInstalledListReturn => {
  const [isLoading, setIsLoading] = useState(false)
  const abortControllerRef = useRef<AbortController | null>(null)

  const [result, setResult] =
    useState<Result<PluginInstalledListData>>(EMPTY_RESULT)

  const fetchPlugins = useCallback(
    async (
      fetchOptions?: FetchOptions<
        UsePluginInstalledListParams,
        PluginInstalledListData
      >,
    ) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const currentController = new AbortController()
      abortControllerRef.current = currentController

      try {
        setIsLoading(true)
        setResult((prev: Result<PluginInstalledListData>) =>
          prev.success
            ? { ...prev, data: { ...prev.data, data: [] } }
            : EMPTY_RESULT,
        )

        const {
          page = DEFAULT_PAGE,
          limit = DEFAULT_PAGE_SIZE,
          sort,
          ...restParams
        } = fetchOptions?.params ?? {}

        const response = await v1PluginInstalledList(
          {
            ...options.params,
            page,
            limit,
            sort,
            ...restParams,
          },
          {
            signal: currentController.signal,
          },
        )

        if (currentController === abortControllerRef.current) {
          const { data, pagination } = response.data
          setResult({
            success: true,
            data: {
              data,
              pagination,
            },
          })
          const resultData = {
            data,
            pagination,
          }
          fetchOptions?.onSuccess?.(resultData)
        }
      } catch (err) {
        if (
          currentController === abortControllerRef.current &&
          !currentController.signal.aborted
        ) {
          setResult({
            success: false,
            error: err as Error,
          })
          fetchOptions?.onError?.(err as Error)
        }
      } finally {
        if (currentController === abortControllerRef.current) {
          setIsLoading(false)
        }
      }
    },
    [result],
  )

  useDeepCompareEffect(() => {
    if (options.params) {
      fetchPlugins({ params: options.params })
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [options.params])

  return {
    result,
    isLoading,
    fetchPlugins,
  }
}

export default usePluginInstalledList
