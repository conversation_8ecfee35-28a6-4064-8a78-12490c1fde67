import { FormInstance } from 'antd'
import { FormItemRender } from 'components/new-dynamic-form'
import { FormContext } from 'components/new-dynamic-form/context'
import { FormItemType } from 'components/new-dynamic-form/types'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { sleep } from 'utils/common'
import { fakeData } from './fake-data'

const Test = () => {
  // const [form] = useForm()
  const renderRef = useRef<{ form: FormInstance }>(null)
  const [data, setData] = useState<Record<string, any>>()
  const [structure, setStructure] = useState<FormItemType>()
  // const [treeRoot, setTreeRoot] = useState<FormItemType>(getRootDefaultValue())

  const initPage = async () => {
    await sleep(2000)
    const { data, structure } = fakeData
    setStructure(structure)
    setData(data)
  }

  // const handleSubmitClick = async () => {
  //   if (form) {
  //     console.error(form.getFieldsValue())
  //     return
  //   }

  //   if (!renderRef.current) return
  //   const validResp = await renderRef.current.form.validateFields()
  //   console.error('submit', validResp)
  // }

  useEffect(() => {
    initPage()
  }, [])

  useEffect(() => {
    if (renderRef.current?.form && data) {
      renderRef.current.form.setFieldsValue(data)
    }
  }, [data])

  // const handleItemUpdate = (item: WithDraggingProps<FormItemType>) => {
  //   console.error('handleItemUpdate')

  //   const { path } = item
  //   const newRoot = cloneDeep(treeRoot)
  //   const newItem = updateItemByPath(newRoot, path, item)

  //   setTreeRoot(newItem)
  // }

  const contextValue = useMemo(
    () => ({
      root: structure,
      setRoot: setStructure,
    }),
    [structure, setStructure],
  )

  return (
    <>
      {/* <Form form={form}>
        {['list'].toString()}
        <Form.List name={'list'} initialValue={[{}]}>
          {(fields, { add, remove }) => {
            return (
              <div>
                {fields.map((f) => {
                  return (
                    <React.Fragment key={f.key}>
                      {[f.name, 'name'].toString()}
                      <Form.Item name={[f.name, 'name']}>
                        <Input />
                      </Form.Item>

                      {[f.name, 'subList'].toString()}
                      <Form.List name={[f.name, 'subList']} initialValue={[{}]}>
                        {(subFields, subOpt) => (
                          <div
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              rowGap: 16,
                            }}
                          >
                            {subFields.map((subField) => (
                              <Space key={subField.key}>
                                {[subField.name, 'first'].toString()}
                                <Form.Item
                                  noStyle
                                  name={[subField.name, 'first']}
                                >
                                  <Input placeholder="first" />
                                </Form.Item>
                                {[subField.name, 'second'].toString()}
                                <Form.Item
                                  noStyle
                                  name={[subField.name, 'second']}
                                >
                                  <Input placeholder="second" />
                                </Form.Item>
                                <div
                                  onClick={() => {
                                    subOpt.remove(subField.name)
                                  }}
                                >
                                  remove
                                </div>
                              </Space>
                            ))}
                            <Button
                              type="dashed"
                              onClick={() => subOpt.add()}
                              block
                            >
                              + Add Sub Item
                            </Button>
                          </div>
                        )}
                      </Form.List>
                    </React.Fragment>
                  )
                })}

                <Button onClick={() => add()}>+++</Button>
              </div>
            )
          }}
        </Form.List>

        <Form.Item noStyle shouldUpdate>
          {() => (
            <span>
              <pre>{JSON.stringify(form.getFieldsValue(), null, 2)}</pre>
            </span>
          )}
        </Form.Item>
      </Form> */}

      {structure && (
        <FormContext.Provider value={contextValue}>
          <FormItemRender
            data={structure?.children}
            ref={renderRef}
            canEdit={false}
          />

          {/* <FormItemConfig
            key={structure?.formItemId}
            data={structure.children[0]}
            onUpdate={handleItemUpdate}
          /> */}
        </FormContext.Provider>
      )}
    </>
  )
}

export default memo(Test)
