import { BranchesOutlined, MessageOutlined } from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { App, Button, Spin, Tag, Tooltip } from 'antd'
import { v1AgentDeleteDelete } from 'api/Api'
import { AgentTypeAgentType } from 'api/data-contracts'
import CustomCard from 'components/custom-card'
import { useExNavigate } from 'hooks/use-ex-navigate'
import { observer } from 'mobx-react'
import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { useGlobalStore } from 'stores/global'
import { Agent } from 'stores/models/agent'
import { getMessageFromError, setQuery } from 'utils/common'
import { formatDate } from 'utils/filter'
import AgentAvatar from 'views/portal/agent/components/agent-avatar'
import {
  AgentCardNavLinkUrl,
  AgentTypeIcons,
  AgentTypes,
  getAgentType,
} from 'views/portal/agent/constants'
import styles from './agent-card.scss'

export enum ActionType {
  Edit = 'edit',
  Delete = 'delete',
}

interface AgentCardProps {
  data: {
    id: number
    uuid: string
    name: string
    description: string
    agentType: AgentTypeAgentType
    iconId?: number
    iconUuid?: string
    organizationId?: number
    updatedAt: string
    createdAt: string
  }
  onAction?: (type: ActionType, agent: Agent) => void
}

export type StudioPageQueryType = {
  id: string
  agentType: keyof typeof AgentTypes
  uuid: string
}

const AgentCard: React.FC<AgentCardProps> = props => {
  const [loading, setLoading] = useState(false)
  const { message, modal } = App.useApp()
  const navigate = useExNavigate()
  const isDevMode = useGlobalStore(state => state.isDeveloperMode)

  const handleModifyButtonClick = () => {
    const params = setQuery({
      id: props.data.id,
      uuid: props.data.uuid,
      agentType: props.data.agentType,
    })

    const basePath = isDevMode
      ? '/portal/agent/studio/workflow'
      : '/portal/agent/studio/analytics'

    navigate(`${basePath}?${params}`, {
      state: { agent: props.data },
    })
  }

  const deleteAgent = async (id: number) => {
    try {
      await v1AgentDeleteDelete({ id })
      return true
    } catch (error) {
      throw error
    }
  }

  const handleMenuClick: MenuProps['onClick'] = e => {
    if (e.key === 'delete') {
      modal.confirm({
        title: 'Are you sure you want to delete this agent?',
        content:
          'This action cannot be undone and will permanently delete the agent from the system.',
        okText: 'Yes, Delete it',
        okType: 'danger',
        cancelText: 'No, Cancel',
        async onOk() {
          setLoading(true)
          try {
            await deleteAgent(props.data.id)
            message.success('The agent has been successfully deleted.')
            props.onAction?.(ActionType.Delete, {
              ...props.data,
              organizationId: props.data.organizationId ?? 0,
              agentName: props.data.name,
              agentIconID: props.data.iconId ?? 0,
              agentIconUUID: props.data.iconUuid ?? '',
              updatedAt: props.data.updatedAt,
              createdAt: props.data.createdAt,
            })
          } catch (error) {
            message.error(getMessageFromError(error))
          }
          setLoading(false)
        },
      })
    } else if (e.key === 'edit') {
      props.onAction?.(ActionType.Edit, {
        ...props.data,
        organizationId: props.data.organizationId ?? 0,
        agentName: props.data.name,
        agentIconID: props.data.iconId ?? 0,
        agentIconUUID: props.data.iconUuid ?? '',
      })
    }
  }

  return (
    <Spin tip="Loading..." spinning={loading}>
      <CustomCard className={styles.agentCard}>
        <CustomCard.Header
          title={props.data.name}
          subTitle={`Update time ${formatDate(props.data.updatedAt)}`}
          icon={
            <AgentAvatar
              agentType={props.data.agentType}
              iconUuid={props.data.iconUuid}
              name={props.data.name}
            />
          }
        />
        <CustomCard.Content>{props.data.description}</CustomCard.Content>
        <CustomCard.Footer
          tags={
            <Tag
              icon={AgentTypeIcons[getAgentType(props.data.agentType)]}
              color="default"
              bordered={false}
            >
              {AgentTypes[getAgentType(props.data.agentType)] ??
                props.data.agentType}
            </Tag>
          }
          actions={[
            <Tooltip key="start" title="Start">
              <Link
                to={`${AgentCardNavLinkUrl[getAgentType(props.data.agentType)]}?agent_uuid=${props.data.uuid}`}
                state={{
                  title: props.data.name,
                  data: {
                    agent: props.data,
                  },
                }}
              >
                <Button
                  type="text"
                  icon={<MessageOutlined />}
                  aria-label="Start chat with agent"
                />
              </Link>
            </Tooltip>,
            <Tooltip key="studio" title="Studio">
              <Button
                type="text"
                icon={<BranchesOutlined />}
                onClick={handleModifyButtonClick}
                aria-label="Open agent studio"
              />
            </Tooltip>,
          ]}
          dropdownItems={[
            {
              label: 'Edit Info',
              key: 'edit',
            },
            {
              label: 'Delete',
              key: 'delete',
            },
          ]}
          handleMenuClick={handleMenuClick}
        />
      </CustomCard>
    </Spin>
  )
}

export default observer(AgentCard)
