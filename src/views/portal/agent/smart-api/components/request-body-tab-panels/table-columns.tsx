import type { TableColumnsType } from 'antd'
import { Form, Typography } from 'antd'
import {
  ValueInput,
  getValueValidationRules,
} from '../../../../../components/value-input'
import type { FormValueEntry, SectionKeys } from '../../types'
import styles from './index.module.scss'

export const tableColumns = (
  sectionKey: SectionKeys,
  selectedKeys: React.Key[] = [],
) =>
  [
    {
      title: 'Key',
      dataIndex: 'key',
      key: sectionKey + ':key',
      fixed: 'left',
      width: 150,
      render: (text: string, _record: FormValueEntry, index: number) => (
        <Form.Item
          className={styles.keyItem}
          name={[index, 'key']}
          style={{ marginBottom: 0 }}
          label={text}
        />
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: sectionKey + ':type',
      minWidth: 150,
      width: 150,
      render: (text: string, _record: FormValueEntry, index: number) => (
        <Form.Item name={[index, 'type']} style={{ marginBottom: 0 }}>
          <Typography.Text>{text || '-'}</Typography.Text>
        </Form.Item>
      ),
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: sectionKey + ':value',
      render: (_: string, record: FormValueEntry, index: number) => {
        const isSelected = selectedKeys.includes(record.key)
        return (
          <Form.Item
            name={[index, 'value']}
            rules={
              isSelected
                ? getValueValidationRules(record.type, record.required)
                : []
            }
            style={{ marginBottom: 0 }}
          >
            <ValueInput type={record.type} required={record.required} />
          </Form.Item>
        )
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: sectionKey + ':description',
      render: (text: string, _record: FormValueEntry, index: number) => (
        <Form.Item name={[index, 'description']} style={{ marginBottom: 0 }}>
          <Typography.Text>{text || '-'}</Typography.Text>
        </Form.Item>
      ),
    },
  ] satisfies TableColumnsType<FormValueEntry>
