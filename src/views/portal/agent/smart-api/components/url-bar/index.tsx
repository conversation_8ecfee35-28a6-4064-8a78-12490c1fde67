import { Typography } from 'antd'

import styles from './index.module.scss'

interface UrlBarProps {
  value?: string
}

export function UrlBar({ value }: Readonly<UrlBarProps>) {
  const fullUrl = `${window.location.origin}${value?.startsWith('/') ? '' : '/'}${value}`
  return (
    <Typography.Text ellipsis className={styles.urlText} title={fullUrl ?? ''}>
      {fullUrl ?? value}
    </Typography.Text>
  )
}

export default UrlBar
