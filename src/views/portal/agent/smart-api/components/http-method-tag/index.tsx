import classNames from 'classnames'
import { HTTP_METHOD_TAG_COLORS, type HttpMethodTagNames } from './constants'
import styles from './index.module.scss'

interface HttpMethodTagProps {
  value?: HttpMethodTagNames
  className?: string
}

function HttpMethodTag({ value, className }: Readonly<HttpMethodTagProps>) {
  return (
    <span
      className={classNames(styles.methodTag, className)}
      style={{ color: HTTP_METHOD_TAG_COLORS[value ?? 'GET'] }}
    >
      {value}
    </span>
  )
}

export default HttpMethodTag
