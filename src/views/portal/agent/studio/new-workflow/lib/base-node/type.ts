import React, { JSX } from 'react'
import {
  Node as <PERSON>act<PERSON><PERSON>Node,
  Edge as <PERSON>act<PERSON>lowEdge,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>andle,
} from '@xyflow/react'
import BaseNode from '.'

export enum NodeTypeEnum {
  Root = 'root',
  Plugin = 'plugin',
  Workflow = 'workflow',
}

type BaseNodeDataType<
  StructureType extends 'instance' | 'data',
  NodeDataType,
  InputsType,
> = {
  id: string
  name: string
  nodeType: string
  parents: StructureType extends 'instance' ? BaseNode[] : string[]
  children: StructureType extends 'instance' ? BaseNode[] : BaseNodeType[]
  readonly nodeData?: NodeDataType
  description: string
  inputs: InputsType
  extends?: Record<string, any>
}

export type BaseViewType =
  | React.ComponentType<any>
  | React.FC<any>
  | ((props: any) => JSX.Element)

export type BaseNodeViews = Partial<{
  Node: BaseViewType
  Sidebar: BaseViewType
}>

export interface BaseNodeType<
  StructureType extends 'instance' | 'data' = 'data',
  NodeDataType = Record<string, any>,
  InputsType = Record<string, any>,
> extends ReactFlowNode<
    BaseNodeDataType<StructureType, NodeDataType, InputsType>
  > {
  data: BaseNodeDataType<StructureType, NodeDataType, InputsType>
  type: NodeTypeEnum
  edges: ReactFlowEdge[]
  handles: ReactFlowHandle[]
}
