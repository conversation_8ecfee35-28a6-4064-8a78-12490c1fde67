import { useWorkflowStore } from 'stores/new-workflow'
import { safeJsonParse } from 'utils/common'
import { NodeInstanceContext } from './context'
import { BaseNodeType, BaseNodeViews, BaseViewType, NodeTypeEnum } from './type'

abstract class BaseNode<
  NodeType extends BaseNodeType<'instance'> = BaseNodeType<'instance'>,
> {
  abstract readonly type: NodeTypeEnum
  node: NodeType
  private readonly views: BaseNodeViews

  constructor(n: BaseNodeType<'data'>, views: BaseNodeViews = {}) {
    const convertDataToNode = (data: BaseNodeType<'data'>) => {
      const instanceNode = {
        ...data,
        data: { ...data.data, parents: [], children: [] },
      }
      return instanceNode as unknown as NodeType
    }

    this.node = convertDataToNode(n)
    this.views = views
  }

  private readonly handleEnhanceViews = (
    props: Record<string, any>,
    View: BaseViewType,
  ) => {
    return (viewProps: Record<string, any>) => (
      <NodeInstanceContext.Provider value={this}>
        <View {...props} {...viewProps} />
      </NodeInstanceContext.Provider>
    )
  }

  getViews = () => {
    return Object.entries(this.views).reduce<BaseNodeViews>((pre, cur) => {
      const [k, View] = cur
      Reflect.set(pre, k, this.handleEnhanceViews({ node: this.node }, View))
      return pre
    }, {})
  }

  toPrint = (): string => {
    // Zustand copy the new object when update, so we need to get the latest node instance from store
    const nodeInstance =
      useWorkflowStore.getState().allNodes?.[this.node.data.id]

    const result = JSON.stringify(nodeInstance?.node, (key, value) => {
      if (key === 'parents' && value?.length) {
        return value.map((v: unknown) => {
          if (v instanceof BaseNode) return v.node.data.id
          return v
        })
      }

      if (key === 'children' && value?.length) {
        return value.map((v: unknown) => {
          if (v instanceof BaseNode) return v.toPrint()
          return v
        })
      }

      if (key === 'freeNodes' && value?.length) {
        return value.map((v: unknown) => {
          if (v instanceof BaseNode) return v.toPrint()
          return v
        })
      }

      if (key === 'propertyForms' || key === 'propertyFormAndEndpointForms')
        return undefined
      return value
    })

    return safeJsonParse(result, '')
  }
}

export { NodeInstanceContext }
export default BaseNode
