import { Handle, HandleProps } from '@xyflow/react'
import cls from 'classnames'
import { nanoid } from 'nanoid'
import { memo, MouseEventHandler } from 'react'

import { useWorkflowStore } from 'stores/new-workflow'
import { capitalizeFirstLetter } from 'utils/format'

import type { BaseNodeType } from '../../lib/base-node/type'
import styles from './index.scss'

export type CustomHandleProps = {
  curNode: BaseNodeType<'instance'>
  handle: Handle
  onClick?: (e: MouseEvent) => void | boolean
  customClassName?: string
} & HandleProps

const CustomHandle = (props: CustomHandleProps) => {
  const { curNode, handle, customClassName, onClick, type, ...resetProps } =
    props
  const { data } = curNode

  const { allNodes, workflowTree, setSidebarConfig, refreshWorkflowTree } =
    useWorkflowStore()

  const handleHandleClick: MouseEventHandler<HTMLDivElement> = e => {
    e.stopPropagation()
    const customClickCallback = onClick?.(e)
    if (customClickCallback) return
    // Custom handle click event
    setSidebarConfig({
      open: true,
      type: 'createNode',
      createNodeInfo: {
        sourceNodeId: data.id,
        sourceHandleId: handle.id,
      },
    })
  }

  const handleConnect: HandleProps['onConnect'] = connection => {
    if (!allNodes || !workflowTree) return
    const { source, target, sourceHandle, targetHandle } = connection
    const sourceNode = Reflect.get(allNodes, source)
    const targetNode = Reflect.get(allNodes, target)
    const edgeId = `${sourceHandle}_${targetHandle}`
    if (!sourceNode || !targetNode) return
    if (sourceNode.node.edges.find(edge => edge.id === edgeId)) return

    if (
      !sourceNode.node.data.children.find(
        child => child.node.id === targetNode.node.id,
      )
    ) {
      sourceNode.node.data.children.push(targetNode)
    }

    sourceNode.node.edges.push({
      id: edgeId,
      source,
      target,
      sourceHandle,
      targetHandle,
      type: 'custom-edge',
    })

    if (
      !targetNode.node.data.parents.find(
        parent => parent.node.id === sourceNode.node.id,
      )
    ) {
      targetNode.node.data.parents.push(sourceNode)
    }
    workflowTree.node.freeNodes = workflowTree.node.freeNodes.filter(
      node => node.node.id !== targetNode.node.id,
    )

    refreshWorkflowTree()
  }

  return (
    <Handle
      className={cls(
        styles.customHandle,
        styles[`customHandle${capitalizeFirstLetter(handle.type)}`],
        customClassName,
      )}
      id={handle.id ?? `${data.name}_${handle.type}_${nanoid(8)}`}
      type={type}
      {...resetProps}
      onConnect={handleConnect}
      onClick={type === 'source' ? handleHandleClick : undefined}
    />
  )
}

export default memo(CustomHandle)
