import { WarningOutlined } from '@ant-design/icons'
import { Space } from 'antd'
import invariant from 'utils/invariant'
import styles from './index.scss'
import type { CheckListMessageType } from './types'

export function Message(props: Readonly<CheckListMessageType>) {
  const iconColor = {
    info: 'var(--ant-color-primary)',
    warning: 'var(--ant-color-warning)',
    error: 'var(--ant-color-error)',
  }[props.type]

  invariant(iconColor, 'Invalid message type')

  return (
    <Space className={styles.message} size="small">
      <WarningOutlined style={{ color: iconColor, fontSize: 16 }} />
      <Space size="small">{props.message}</Space>
    </Space>
  )
}
