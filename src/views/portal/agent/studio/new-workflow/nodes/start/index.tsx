import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'
import { nanoid } from 'nanoid'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node/type'
import WorkflowNode, { WorkflowNodeDataType } from '../../lib/workflow-node'
import NodeDefaultNode from '../node-default'
import { output, propertyForm } from './constants'

import StartSidebarView from './sidebar'

class StartNode extends WorkflowNode {
  static readonly NodeView = NodeDefaultNode.NodeView
  static readonly SidebarView = StartSidebarView

  static readonly getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>,
  ): WorkflowNodeDataType => {
    const defaultCfg: WorkflowNodeDataType = {
      id,
      type: NodeTypeEnum.Workflow,
      edges: [],
      position: { x: 0, y: 0 },
      handles: [
        {
          id: `${id}_source_${nanoid(4)}`,
          type: 'source',
          position: Position.Right,
          nodeId: id,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
      ],
      data: {
        id,
        name: 'Start',
        nodeType: 'start',
        description: 'Start Node',
        nodeData: {
          name: 'Start',
          iconId: '',
          type: 'internal',
          description: 'Start Node',
          propertyForms: [
            { name: 'Chatbot Start', structure: propertyForm, output },
            { name: 'SmartAPI Start', structure: propertyForm, output },
          ],
          // According the workflow type choose one. (SmartApi or Chatbot)
          propertyForm: {
            name: 'Chatbot Start',
            structure: propertyForm,
            output,
          },
        },
        inputs: {
          propertyForm: {
            settings: {
              chat_to_document: false,
              max_document_size: 0,
              chat_to_image: false,
              voice_conversation: false,
            },
            custom_variables: {
              list_QKgw: [],
            },
            pii_filter: {
              enable_pii_filter: false,
              threshold: 0,
              labels: [],
              whitelist: [],
            },
            regex_filter: {
              enable_for_documents: false,
              regex_items: [],
            },
          },
        },
        parents: [],
        children: [],
      },
    }
    return merge(defaultCfg, cfg)
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, { Node: NodeDefaultNode.NodeView, Sidebar: StartSidebarView })
  }
}

export default StartNode
