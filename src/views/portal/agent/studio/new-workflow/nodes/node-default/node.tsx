import { memo } from 'react'
import { NodeProps, Position } from '@xyflow/react'
import { WorkflowNodeDataType, WorkflowNodeType } from '../../lib/workflow-node'
import { PluginNodeDataType, PluginNodeType } from '../../lib/plugin-node'

import CustomHandle from '../../components/custom-handle'
import NodeWrapper from '../../components/node-wrapper'

import styles from './index.scss'

type DefaultNodeViewProps = NodeProps<
  PluginNodeDataType | WorkflowNodeDataType
> & {
  node: PluginNodeType | WorkflowNodeType
}

const DefaultNodeView = (props: DefaultNodeViewProps) => {
  const { node } = props

  return (
    <div className={styles.nodeDefaultWrapper}>
      {node?.handles
        ?.filter(h => h.type === 'target')
        .map(handle => (
          <CustomHandle
            key={handle.id}
            curNode={node}
            handle={handle}
            type="target"
            position={Position.Left}
            isConnectable={props.isConnectable}
          />
        ))}

      {node?.handles
        ?.filter(h => h.type === 'source')
        .map(handle => (
          <CustomHandle
            key={handle.id}
            curNode={node}
            handle={handle}
            type="source"
            position={Position.Right}
            isConnectable={props.isConnectable}
          />
        ))}
    </div>
  )
}

export default NodeWrapper(memo(DefaultNodeView))
