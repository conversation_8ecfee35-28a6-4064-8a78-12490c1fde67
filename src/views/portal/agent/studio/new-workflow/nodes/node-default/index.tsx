import { BaseNodeType } from '../../lib/base-node/type'
import PluginNode from '../../lib/plugin-node'

import NodeDefaultNodeView from './node'
import NodeDefaultSidebarView from './sidebar'

export type NodeDefaultNodeDataType = {}

class NodeDefaultNode extends PluginNode {
  static readonly NodeView = NodeDefaultNodeView
  static readonly SidebarView = NodeDefaultSidebarView

  constructor(node: BaseNodeType<'data'>) {
    super(node, {
      Node: NodeDefaultNodeView,
      Sidebar: NodeDefaultSidebarView,
    })
  }
}

export default NodeDefaultNode
