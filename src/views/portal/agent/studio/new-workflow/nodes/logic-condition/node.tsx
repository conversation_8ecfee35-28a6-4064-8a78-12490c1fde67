import { <PERSON><PERSON>, NodeProps, Position } from '@xyflow/react'
import { useEffect, useMemo, useState } from 'react'
import {
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType,
  WorkflowNodeType,
} from '../../lib/workflow-node'

import CustomHandle from '../../components/custom-handle'
import NodeWrapper from '../../components/node-wrapper'

import { ConditionGroup } from 'components/new-dynamic-form/components/condition'
import { useWorkflowStore } from 'stores/new-workflow'
import { BaseNodeType } from '../../lib/base-node/type'
import styles from './index.scss'

export type LogicConditionNodeDataType = BaseNodeType<
  'data',
  WorkflowNodeBaseDataType,
  WorkflowNodeInputsDataType & {
    outputBranch: Record<string, Array<string>>
    propertyForm: Record<string, any> & {
      conditionBranches: { branches: ConditionGroup }
    }
  }
>

type LogicConditionNodeViewProps = NodeProps<LogicConditionNodeDataType> & {
  node: WorkflowNodeType
}

type SourceHandle = Handle & { conditionName: string }

const LogicConditionNodeView = (props: LogicConditionNodeViewProps) => {
  const { id, data, node } = props

  const { allNodes, setNode } = useWorkflowStore()
  const [sourceHandles, setSourceHandles] = useState<SourceHandle[]>([])
  const elseHandle = useMemo<SourceHandle>(
    () => ({
      id: `source_else`,
      conditionName: `Else`,
      type: 'source',
      position: Position.Right,
      nodeId: id,
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    }),
    [],
  )

  const targetHandles = node?.handles?.filter(h => h.type === 'target') ?? []

  useEffect(() => {
    const branches = data.inputs?.propertyForm?.conditionBranches.branches
    if (!branches) return

    const newHandles = branches.map(branch => conditionToHandle(branch, node))
    setSourceHandles([...newHandles, elseHandle])

    if (allNodes) {
      const node = Reflect.get(allNodes, id)
      node.node.handles = [...targetHandles, ...newHandles, elseHandle]
      node.node.edges = node.node.edges?.filter(edge => {
        const sourceHandle = edge.sourceHandle?.split('_').pop()
        return (
          sourceHandle === 'else' || branches.find(b => b.id === sourceHandle)
        )
      })
      setNode(node.node.id, node)
    }
  }, [node?.data.inputs?.propertyForm?.conditionBranches.branches])

  useEffect(() => {
    if (!allNodes) return
    const node = Reflect.get(allNodes, id)
    const { data, edges } = node.node
    if (!data) return
    data.inputs = {
      ...node.node.data.inputs,
      outputBranch: edges.reduce<Record<string, Array<string>>>((pre, cur) => {
        const { sourceHandle, target } = cur
        const conditionId = sourceHandle?.split('_').pop()
        if (!conditionId) return pre
        pre[conditionId] = pre[conditionId] ?? []
        pre[conditionId].push(target)
        return pre
      }, {}),
    }
  }, [node.edges])

  return (
    <div className={styles.logicConditionNodeWrapper}>
      {targetHandles.map(handle => (
        <CustomHandle
          key={handle.id}
          curNode={node}
          handle={handle}
          type="target"
          position={Position.Left}
          isConnectable={props.isConnectable}
        />
      ))}

      <div className={styles.logicConditionNodeBranchWrapper}>
        {sourceHandles?.map?.(handle => (
          <div
            key={handle.conditionName}
            className={styles.logicConditionNodeBranchItem}
          >
            <span>{handle.conditionName}</span>
            <CustomHandle
              key={handle.id}
              curNode={node}
              handle={handle}
              type="source"
              position={Position.Right}
              isConnectable={props.isConnectable}
              customClassName={styles.logicConditionNodeBranchItemHandler}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

const conditionToHandle = (
  condition: ConditionGroup[number],
  node: WorkflowNodeType,
): SourceHandle => ({
  id: `${node.id}_source_${condition.id}`,
  conditionName: condition.groupName,
  type: 'source',
  position: Position.Right,
  nodeId: node.id,
  x: 0,
  y: 0,
  width: 0,
  height: 0,
})

export default NodeWrapper(LogicConditionNodeView)
