import { nanoid } from 'nanoid'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node/type'
import WorkflowNode, { WorkflowNodeDataType } from '../../lib/workflow-node'
import NodeDefaultNode from '../node-default'

import { Position } from '@xyflow/react'
import { merge } from 'lodash-es'
import { output, structure } from './constants'
import EndSidebarView from './sidebar'

class EndNode extends WorkflowNode {
  readonly type = NodeTypeEnum.Workflow
  static readonly NodeView = NodeDefaultNode.NodeView
  static readonly SidebarView = EndSidebarView
  static readonly getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>,
  ): WorkflowNodeDataType => {
    const defaultCfg: WorkflowNodeDataType = {
      type: NodeTypeEnum.Workflow,
      handles: [
        {
          id: `${id}_target_${nanoid(4)}`,
          type: 'target',
          position: Position.Left,
          nodeId: id,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
      ],
      data: {
        name: 'End',
        nodeType: 'end',
        description: 'End node',
        nodeData: {
          name: 'End',
          iconId: '',
          type: 'internal',
          description: 'End node',
          propertyForm: {
            name: 'End',
            structure,
            output,
          },
        },
        id,
        inputs: {},
        parents: [],
        children: [],
      },
      id,
      edges: [],
      position: { x: 0, y: 0 },
    }
    return merge(defaultCfg, cfg)
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, { Node: NodeDefaultNode.NodeView, Sidebar: EndSidebarView })
  }
}

export default EndNode
