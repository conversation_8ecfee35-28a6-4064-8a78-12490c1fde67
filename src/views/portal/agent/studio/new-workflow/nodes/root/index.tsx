import BaseNode from '../../lib/base-node'
import { BaseNodeType, NodeTypeEnum } from '../../lib/base-node/type'

import { merge } from 'lodash-es'
import RootNodeView from './node'
import RootSidebarView from './sidebar'

export type RootNodeType<StructureType extends 'instance' | 'data'> =
  BaseNodeType<StructureType> & {
    freeNodes: StructureType extends 'instance'
      ? BaseNode[]
      : BaseNodeType<'data'>[]
  }

class RootNode extends BaseNode<RootNodeType<'instance'>> {
  readonly type = NodeTypeEnum.Root
  static readonly NodeView = RootNodeView
  static readonly SidebarView = RootSidebarView

  static readonly getDefaultNodeData = (
    id: string,
    cfg?: Partial<BaseNodeType<'data'>>,
  ): RootNodeType<'data'> => {
    return merge(
      {
        id,
        type: NodeTypeEnum.Root,
        handles: [],
        edges: [],
        position: { x: 0, y: 0 },
        data: {
          id,
          name: 'Root',
          nodeType: 'root',
          description: 'Root Node',
          parents: [],
          children: [],
        },
        freeNodes: [],
      },
      cfg,
    )
  }

  constructor(node: BaseNodeType<'data'>) {
    super(node, { Node: RootNodeView, Sidebar: RootSidebarView })
  }
}

export default RootNode
