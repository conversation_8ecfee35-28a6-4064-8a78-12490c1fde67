import RootNode from './root'
import NodeDefaultNode from './node-default'
import StartNode from './start'
import EndNode from './end'
import LogicConditionNode from './logic-condition'

export const WorkflowTypeNodes = {
  start: StartNode,
  end: EndNode,
  logicCondition: LogicConditionNode,
}

export const WorkflowNodeMap = {
  root: RootNode,
  nodeDefault: NodeDefaultNode,
  ...WorkflowTypeNodes,
}
