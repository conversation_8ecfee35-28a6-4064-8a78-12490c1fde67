import { MarkerType } from '@xyflow/react'
import React from 'react'

import { useWorkflowStore } from 'stores/new-workflow'
import CustomEdge from './components/custom-edge'

import type { EdgeTypes, NodeProps, NodeTypes } from '@xyflow/react'
import type { BaseNodeType } from './lib/base-node/type'
import { WorkflowNodeMap } from './nodes/constant'
import EndNode from './nodes/end'
import RootNode from './nodes/root'
import StartNode from './nodes/start'

const useGetNodeView = (
  node: NodeProps & { data: BaseNodeType<'data'>['data'] },
  Cmp: React.ComponentType<any>,
) => {
  const { allNodes } = useWorkflowStore()
  if (!allNodes || !node) return null
  const curNode = Reflect.get(allNodes, node.data.id)?.node
  return <Cmp {...node} node={curNode} />
}

export const nodeTypes = Object.entries(WorkflowNodeMap).reduce<NodeTypes>(
  (pre, cur) => {
    const [k, v] = cur
    if (k === 'root') {
      Reflect.set(pre, k, () => <></>)
      return pre
    } else {
      Reflect.set(
        pre,
        k,
        (node: NodeProps & { data: BaseNodeType<'data'>['data'] }) =>
          useGetNodeView(node, v.NodeView),
      )
    }
    return pre
  },
  {},
)

export const edgeTypes: EdgeTypes = {
  'custom-edge': CustomEdge,
}

export const defaultEdgeOptions = {
  animated: true,
  markerEnd: { type: MarkerType.ArrowClosed },
  style: { strokeWidth: 2 },
}

export const getDefaultTree = () => {
  const root = new WorkflowNodeMap.root(RootNode.getDefaultNodeData('root'))

  const start = new WorkflowNodeMap.start(
    StartNode.getDefaultNodeData('start', { position: { x: 0, y: 0 } }),
  )

  const end = new WorkflowNodeMap.end(
    EndNode.getDefaultNodeData('end', { position: { x: 400, y: 0 } }),
  )

  root.node.data.children.push(start)
  start.node.data.parents.push(root)
  start.node.data.children.push(end)
  end.node.data.parents.push(start)

  start.node.edges.push({
    id: `${start.node.handles.filter(h => h.type === 'source')[0].id}_to_${
      end.node.handles.filter(h => h.type === 'target')[0].id
    }`,
    source: start.node.id,
    target: end.node.id,
    sourceHandle: start.node.handles[0].id,
    targetHandle: end.node.handles[0].id,
    type: 'custom-edge',
  })

  return root
}
