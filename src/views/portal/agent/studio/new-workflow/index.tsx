import { SaveOutlined } from '@ant-design/icons'
import {
  Background,
  Controls,
  Edge,
  MiniMap,
  Node,
  OnEdgesChange,
  OnNodesChange,
  ReactFlow,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from '@xyflow/react'
import { <PERSON><PERSON>, Modal } from 'antd'
import dayjs from 'dayjs'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'

import {
  AgentTypeAgentType,
  ResponsesWorkflowResponse,
} from 'api/data-contracts'
import Send from 'assets/images/send.svg'
import { useMainLayoutContext } from 'layouts/portal/page-main-layout/context'
import { useWorkflowStore } from 'stores/new-workflow'
import { getQuery, safeJsonParse, sleep } from 'utils/common'
import { isLocal } from 'utils/env'
import { defaultEdgeOptions } from '../workflow/constants'
import { edgeTypes, getDefaultTree, nodeTypes } from './constants'
import BaseNode from './lib/base-node'
import {
  convertDataToNodeTree,
  convertNodeTreeToReactFlow,
  ReactFlowTreeType,
} from './utils'

import AgentAvatar from '../../components/agent-avatar'
import CheckList from './components/checklist'
import { CheckListIcon } from './components/checklist/check-list-icon'
import Sidebar from './components/sidebar'

import {
  v1WorkflowGetLatestWorkflowByAgentList,
  v2WorkflowPublishCreate,
  v2WorkflowSaveCreate,
} from 'api/Api'
import StaticFunction from 'components/antd-static-function'
import styles from './index.scss'
import RootNode, { RootNodeType } from './nodes/root'

type WorkflowPageQueryType = {
  id: string
}

const { message } = StaticFunction

const Workflow = () => {
  const { id: agentId } = getQuery<WorkflowPageQueryType>(location.search)

  const {
    workflowTree,
    allNodes,
    sidebarConfig,
    checkListData,
    setWorkflowTree,
    setNode,
    setNodeThrottle,
    setSidebarConfig,
    refreshWorkflowTree,
  } = useWorkflowStore()
  const { setRegisterEvents, setCaption, setTitle, setIcon, setActions } =
    useMainLayoutContext() ?? {}

  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([])
  const [lastSaveTime, setLastSaveTime] = useState<dayjs.Dayjs | null>(null)
  const [checkListModalOpen, setCheckListModalOpen] = useState(false)
  const [workflow, setWorkflow] = useState<ResponsesWorkflowResponse>()
  const caption = useMemo(() => {
    return lastSaveTime ? lastSaveTime.fromNow() : 'Not saved yet'
  }, [lastSaveTime])

  const fetchData = async () => {
    const workflowResp = await v1WorkflowGetLatestWorkflowByAgentList({
      agentID: Number(agentId),
    })

    if (!workflowResp.data) return
    setWorkflow(workflowResp.data)
    const { nodes } = workflowResp.data

    setIcon?.(
      <AgentAvatar
        agentType={AgentTypeAgentType.Chatbot}
        iconUuid={workflowResp.data?.agent.agentIconUUID}
      />,
    )
    setTitle?.(workflowResp.data?.agent.agentName)

    if (nodes) {
      const parsedNodes = safeJsonParse<RootNodeType<'data'>>(nodes)
      if (!parsedNodes) return
      const tree = convertDataToNodeTree(parsedNodes)
      if (tree instanceof RootNode) setWorkflowTree(tree)
    } else {
      const defaultTree = getDefaultTree()
      setWorkflowTree(defaultTree)
    }
  }

  const handleNodesChange: OnNodesChange = useCallback(
    changes => {
      if (!allNodes) return

      changes.forEach((change: any) => {
        const { type, id, position } = change

        if (type === 'position') {
          const currentNode: BaseNode = Reflect.get(allNodes, id)

          if (!currentNode) return
          Reflect.set(currentNode.node, 'position', position)
          setNodeThrottle(id, currentNode)
        }
      })

      onNodesChange(changes)
    },
    [onNodesChange, allNodes],
  )

  const handleEdgesChange: OnEdgesChange = useCallback(
    changes => {
      if (!allNodes) return
      changes.forEach((change: any) => {
        const { type, id } = change

        if (type === 'remove') {
          const edge = edges.find(edge => edge.id === id)
          if (!edge) return
          const sourceNode = Reflect.get(allNodes, edge?.source)
          if (!sourceNode) return

          sourceNode.node.edges = sourceNode.node.edges?.filter(
            edge => edge.id !== id,
          )
          sourceNode.node.data.children = sourceNode.node.data.children.filter(
            child => child.node.id !== edge?.target,
          )

          const targetNode = Reflect.get(allNodes, edge?.target)
          targetNode.node.data.parents = targetNode.node.data.parents.filter(
            parent => parent.node.id !== sourceNode.node.id,
          )

          if (!targetNode.node.data.parents.length && workflowTree) {
            workflowTree.node.freeNodes.push(targetNode)
          }

          setNode(sourceNode.node.data.id, sourceNode)
          setNode(targetNode.node.data.id, targetNode)
          refreshWorkflowTree()
        }
      })

      onEdgesChange(changes)
    },
    [onEdgesChange, allNodes, edges],
  )

  const handlePaneClick = () => {
    if (sidebarConfig.type === 'createNode')
      setSidebarConfig({ open: false, type: '', createNodeInfo: undefined })
  }

  const handleWorkflowSave = () => {
    setLastSaveTime(dayjs())
    if (!workflow?.id) return
    v2WorkflowSaveCreate({
      id: workflow.id,
      nodes: JSON.stringify(workflowTree?.toPrint()) ?? '',
    })
      .then(() => {
        message.success('Workflow saved successfully')
      })
      .catch(error => {
        message.error('Failed to save workflow')
      })
  }

  const handleWorkflowPublish = () => {
    if (!workflow?.id) return
    v2WorkflowPublishCreate({
      id: workflow.id,
      nodes: JSON.stringify(workflowTree?.toPrint()) ?? '',
    })
      .then(() => {
        message.success('Workflow published successfully')
        setLastSaveTime(dayjs())
      })
      .catch(error => {
        message.error('Failed to publish workflow')
      })
  }

  const initCaption = () => {
    setCaption?.(caption)
  }

  const handleTest = () => {
    console.group('Test')
    console.error({
      workflowTree,
      workflowTreeOutput: workflowTree?.toPrint(),
    })
    console.error({ nodes, edges })
    console.groupEnd()
  }

  const handleRest = () => {
    setWorkflowTree(getDefaultTree())
  }

  const initReactFlowCanvas = async (reactFlowTree: ReactFlowTreeType) => {
    if (!reactFlowTree) return
    setNodes(reactFlowTree.nodes ?? [])
    // Wait for nodes to be rendered
    await sleep(300)
    setEdges(reactFlowTree.edges ?? [])
  }

  useEffect(() => {
    fetchData()
    setRegisterEvents?.([
      initCaption,
      () => setActions?.(headerActionOperations),
    ])
  }, [])

  useEffect(() => {
    // allNodes update with workflow. Because allNodes has more effect
    // Keep the nodes don't change too many times
    if (!allNodes) return
    const reactFlowInfo = convertNodeTreeToReactFlow(allNodes)
    initReactFlowCanvas(reactFlowInfo)
  }, [workflowTree])

  useEffect(() => {
    initCaption()
  }, [lastSaveTime])

  // Render
  const headerActionOperations = useMemo(() => {
    const actionItems = [
      {
        key: 'save',
        icon: <SaveOutlined style={{ fontSize: 20 }} />,
        onClick: handleWorkflowSave,
        // disabled: !hasChanges,
        text: 'Save',
        style: { fontWeight: 700 },
      },
      {
        key: 'checklist',
        icon: <CheckListIcon isChecked={!!checkListData.length} />,
        onClick: () => setCheckListModalOpen(true),
        text: 'Checklist',
        style: { fontWeight: 700 },
      },
      {
        key: 'publish',
        icon: <Send style={{ fontSize: 20, verticalAlign: 'middle' }} />,
        onClick: handleWorkflowPublish,
        text: 'Publish',
        style: { fontWeight: 700 },
      },
    ]
    return actionItems
  }, [checkListData.length, workflow, workflowTree])

  useEffect(() => {
    setActions?.(headerActionOperations)
  }, [headerActionOperations])

  return (
    <div className={styles.workflowWrapper}>
      {isLocal && (
        <div style={{ position: 'absolute', zIndex: 1, bottom: 0, right: 0 }}>
          <Button onClick={handleTest}>Data</Button>
          <Button onClick={handleRest}>Reset</Button>
        </div>
      )}
      <ReactFlow
        fitView
        defaultEdgeOptions={defaultEdgeOptions}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onPaneClick={handlePaneClick}
      >
        <Background gap={10} size={2} color="#EAECEF" />
        <Sidebar />

        <div className={styles.workflowToolWrapper}>
          <Controls
            position={'top-left'}
            orientation={'horizontal'}
            className={styles.workflowControls}
          />
          <MiniMap position={'top-left'} className={styles.workflowMiniMap} />
        </div>

        <Modal
          title="Checklist"
          open={checkListModalOpen}
          onCancel={() => setCheckListModalOpen(false)}
          footer={null}
        >
          <span>Make sure all issues are resolved before publish</span>
          {checkListData.length ? (
            <CheckList items={checkListData} />
          ) : (
            <div>No issues</div>
          )}
        </Modal>
      </ReactFlow>
    </div>
  )
}

const WorkflowWrapper = () => {
  return (
    <ReactFlowProvider>
      <Workflow />
    </ReactFlowProvider>
  )
}

export default memo(WorkflowWrapper)
