import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Position, NodeProps, useReactFlow } from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { FilterOutlined } from '@ant-design/icons'
import WorkflowStore from 'stores/workflow'
import { observer } from 'mobx-react'
import {
  OutputField,
  InputField,
  DataType,
  InputType,
  CustomNodeProps,
  NodeData,
} from 'views/portal/agent/studio/workflow/model'
import DeleteButton from '../../components/delete-button'
import { getNewNodeLabel } from '../../utils'
import CustomHandle from '../../components/custom-handle'

// Define default values
const defaultInputs: InputField[] = [
  {
    name: 'content',
    type: 'input' as InputType,
    dataType: 'String' as DataType,
    value: '',
    reference: '',
  },
  {
    name: 'placeholder',
    type: 'input' as InputType,
    dataType: 'String' as DataType,
    value: '***',
    reference: '',
  },
  {
    name: 'threshold',
    type: 'input' as InputType,
    dataType: 'Number' as DataType,
    value: 0.3,
    reference: '',
  },
  {
    name: 'labels',
    type: 'input' as InputType,
    dataType: 'Array<String>' as DataType,
    value: [],
    reference: '',
  },
  {
    name: 'whitelist',
    type: 'input' as InputType,
    dataType: 'Array<String>' as DataType,
    value: [],
    reference: '',
  },
]

const defaultOutputs: OutputField[] = [
  {
    name: 'text',
    type: 'String' as DataType,
    description: 'Filtered Text',
    required: true,
    children: [],
  },
]

const getDefaultNodeData = (label: string): NodeData => ({
  label,
  description: 'Filter out personally identifiable information',
  input: defaultInputs,
  output: defaultOutputs,
  intentBranch: [],
  conditionBranch: [],
  branchOutput: {},
})

const PIIFilterNode: React.FC<CustomNodeProps> = observer(node => {
  const { getNodes } = useReactFlow()
  const [nodeData, setNodeData] = useState<NodeData>(
    node.data.data || getDefaultNodeData('PII Filter'),
  )

  useEffect(() => {
    if (!node.data.data) {
      const newLabel = getNewNodeLabel(getNodes(), 'PII')
      const newNodeData = getDefaultNodeData(newLabel)
      setNodeData(newNodeData)
      if (node.data.onChange) {
        node.data.onChange(node.id, { data: newNodeData })
      }
    } else if (node.data.data.label !== nodeData.label) {
      setNodeData(node.data.data)
    }
  }, [node.data, getNodes])

  useEffect(() => {
    if (node.selected) {
      WorkflowStore.selectNode(node)
    }
  }, [node.selected])

  return (
    <div
      className={
        node.id === WorkflowStore.selectedNode?.id
          ? 'custom-node pii-filter-node active'
          : 'custom-node pii-filter-node'
      }
    >
      <CustomHandle
        node={node}
        type="target"
        position={Position.Left}
        isConnectable={node.isConnectable}
      />
      <div className="node-title">
        <div className="icon">
          <FilterOutlined />
        </div>
        <div className="text">{nodeData.label}</div>
      </div>
      <div className="node-desc">{nodeData.description}</div>
      <DeleteButton nodeID={node.id} />
      <CustomHandle
        node={node}
        type="source"
        position={Position.Right}
        isConnectable={node.isConnectable}
      />
    </div>
  )
})

export default PIIFilterNode
