import { observer } from 'mobx-react'
import { Outlet, useNavigate } from 'react-router-dom'

import BarChart from 'assets/images/bar-chart.svg'
import Branch from 'assets/images/branch.svg'
import PieChart from 'assets/images/pie-chart.svg'

import PageMainLayout from 'layouts/portal/page-main-layout'

import './index.css'
import { useGlobalStore } from 'stores/global'
import { isLocal } from 'utils/env'

const headerMenuItems = [
  {
    text: 'Workflow',
    value: `/portal/agent/studio/workflow`,
    icon: <Branch fontSize={24} />,
  },
  {
    text: 'Evaluation',
    value: `/portal/agent/studio/evaluation`,
    icon: <PieChart fontSize={24} />,
  },
  {
    text: 'Analytics',
    value: `/portal/agent/studio/analytics`,
    icon: <BarChart fontSize={24} />,
  },
]

if (isLocal) {
  headerMenuItems.unshift({
    text: 'New Workflow',
    value: `/portal/agent/studio/new-workflow`,
    icon: <Branch fontSize={24} />,
  })
}

const Studio = () => {
  const isDevMode = useGlobalStore(state => state.isDeveloperMode)
  const navigate = useNavigate()

  return (
    <PageMainLayout
      menu={headerMenuItems.filter(i => isDevMode || i.text !== 'Workflow')}
      onClickBack={() => navigate('/portal/agent')}
    >
      <Outlet />
    </PageMainLayout>
  )
}

export default observer(Studio)
