import { v1ChatConversationsStatisticsPeriodicList } from 'api/Api'
import { ResponsesConversationStatsWithDetailResponse } from 'api/data-contracts'
import { ISO_8601_DATE_TIME_FORMAT } from 'constants/common'
import * as echarts from 'echarts'
import { useEffect, useRef, useState } from 'react'
import { fetchWithCache, isEmpty } from 'utils/common'
import { currentTimezone, formatDate } from 'utils/filter'
import LoadingWrapper from '../../loading-wrapper'
import { PeriodType } from '../../section-card'

export interface ConversationInteractionsChartProps {
  agentId: number
  startAt: string
  endAt: string
  timezoneOffset?: number
  period: PeriodType
}

const ConversationInteractionsChart: React.FC<
  ConversationInteractionsChartProps
> = props => {
  const chartRef = useRef<HTMLDivElement>(null)
  const [chart, setChart] = useState<echarts.ECharts>()
  const [resData, setResData] =
    useState<ResponsesConversationStatsWithDetailResponse[]>()
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const handleResize = () => {
      chart?.resize()
    }
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [chart])

  useEffect(() => {
    if (isEmpty(props.startAt) || isEmpty(props.endAt)) {
      return
    }

    setLoading(true)
    const timezoneOffset = props.timezoneOffset ?? currentTimezone()
    fetchWithCache<typeof v1ChatConversationsStatisticsPeriodicList>(
      v1ChatConversationsStatisticsPeriodicList,
      {
        agent_id: props.agentId,
        start_at: formatDate(props.startAt, ISO_8601_DATE_TIME_FORMAT),
        end_at: formatDate(props.endAt, ISO_8601_DATE_TIME_FORMAT),
        timezone_offset: timezoneOffset,
        period: props.period,
        day_of_week: 1,
        day_of_month: 1,
        month_of_year: 1,
      },
    )
      .then(res => {
        setResData(res.data)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [
    props.agentId,
    props.startAt,
    props.endAt,
    props.timezoneOffset,
    props.period,
  ])

  useEffect(() => {
    const chart = echarts.init(chartRef.current as HTMLDivElement)
    setChart(chart)
  }, [chartRef])

  useEffect(() => {
    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        position: function (pt) {
          return [pt[0], '10%']
        },
      },
      color: ['#3278d9'],
      xAxis: {
        type: 'category',
        data: resData?.map(item => formatDate(item.startAt)) || [],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: resData?.map(item => item.totalInteractions) || [],
          type: 'bar',
        },
      ],
    }
    chart?.setOption(option)
  }, [chart, resData])

  return (
    <LoadingWrapper loading={loading}>
      <div ref={chartRef} style={{ width: '100%', height: '100%' }}></div>
    </LoadingWrapper>
  )
}

export default ConversationInteractionsChart
