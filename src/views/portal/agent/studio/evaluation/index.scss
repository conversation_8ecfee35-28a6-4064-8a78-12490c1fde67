.evaluationWrapper {
  overflow: hidden;
  padding-inline: 16px;
  flex-grow: 1;
}

.evaluationTabs {
  height: calc(100vh - 2 * 24px - 38px - 24px * 2 - 6px);
  margin-block-start: 24px;

  :global(.ant-tabs-nav) {
    padding-block-end: 2px;

    &::before {
      border-bottom: 1px solid #f7f9fa;
    }
  }

  :global {
    .ant-tabs-tab-active {
      font-weight: 700;
    }

    .ant-tabs-content,
    .ant-tabs-tabpane {
      height: 100%;
    }
  }
}
