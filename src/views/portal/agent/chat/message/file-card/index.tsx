import { Avatar, Flex, Tooltip } from 'antd'
import { useMemo } from 'react'
import { getFileIcon } from 'utils/common'
import { formatFileSize } from 'utils/filter'
import styles from './index.scss'

export interface FileCardProps {
  name: string
  ext?: string
  size?: number
}

const FileCard: React.FC<FileCardProps> = props => {
  const ext = useMemo(() => {
    const fileName = props.ext ?? props.name
    const match = /.*\.(\w+)/.exec(fileName ?? '')
    return match?.[1]
  }, [props.ext, props.name])
  return (
    <Flex className={styles.fileCardWrapper} gap={12}>
      <Avatar
        className={styles.fileCardIcon}
        shape="square"
        size={48}
        icon={getFileIcon(ext ?? '')}
      />
      <Flex className={styles.fileCardBody} justify="center" gap={12} vertical>
        <Tooltip title={props.name?.length > 20 ? props.name : undefined}>
          <div className={styles.fileCardName}>{props.name}</div>
        </Tooltip>
        <Flex className={styles.fileCardMeta} gap={4}>
          {ext && (
            <>
              <span>{ext}</span>·
            </>
          )}
          {props.size && <span>{formatFileSize(props.size)}</span>}
        </Flex>
      </Flex>
    </Flex>
  )
}

export default FileCard
