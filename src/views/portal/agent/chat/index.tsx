import {
  CalendarOutlined,
  LeftOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from '@ant-design/icons'
import {
  Avatar,
  Button,
  Dropdown,
  Empty,
  Flex,
  Input,
  Menu,
  MenuProps,
  Skeleton,
  Space,
  Splitter,
  UploadProps,
} from 'antd'
import {
  v1AgentList,
  v1ChatConversationsList,
  v1ChatConversationsUploadDocumentCreate,
  v1ChatSessionsList,
  v1PublishchannelMainList,
  v1WorkflowGetLatestWorkflowByAgentList,
} from 'api/Api'
import {
  GenieCoreAppHandlersChatRequestsSendRequest,
  GenieCoreAppHandlersChatResponsesDocumentMetadataResponse,
  ModelsConversationRoleType,
  ResponsesAgentResponse,
  ResponsesWorkflowResponse,
} from 'api/data-contracts'
import { useExNavigate } from 'hooks/use-ex-navigate'
import { isEmpty } from 'lodash-es'
import { observer } from 'mobx-react'
import React, {
  CSSProperties,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { Link, useSearchParams } from 'react-router-dom'
import apiConfig from 'services/api'
import { createStreamingApiRequest } from 'services/request'
import userStore from 'stores/user'
import { getFileUrl, getUserLanguage } from 'utils/common'
import { dayjsUTC } from 'utils/filter'
import ChatInput, { ChatInputRef } from './chat-input'
import styles from './index.scss'
import Message, { FeedbackLabel, MessageAction } from './message'
import SearchModal from './search-modal'
import SessionItem from './session-item'
import {
  ChatConversation,
  ChatMainChannel,
  ChatSession,
  SendResponse,
} from './types'

const supportedImageTypes = ['image/jpeg', 'image/png', 'image/gif']
const supportedDocumentTypes = [
  'application/pdf',
  'application/msword',
  // 'application/vnd.ms-excel',
  // 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
]

const dateRangeItems: MenuProps['items'] = [
  {
    key: '9999',
    label: <span>All days</span>,
  },
  {
    key: '1',
    label: <span>Today</span>,
  },
  {
    key: '7',
    label: <span>Last 7 days</span>,
  },
  {
    key: '14',
    label: <span>Last 14 days</span>,
  },
  {
    key: '30',
    label: <span>Last 30 days</span>,
  },
  {
    key: '90',
    label: <span>Last 90 days</span>,
  },
  {
    key: '180',
    label: <span>Last 180 days</span>,
  },
]

const groupSessions = (sessions: ChatSession[]) => {
  const groupedSessions: {
    period: string
    sessions: ChatSession[]
  }[] = [
    { period: 'Today', sessions: [] },
    { period: 'Yesterday', sessions: [] },
    { period: 'Previous 7 Days', sessions: [] },
    { period: 'Previous 30 Days', sessions: [] },
    { period: 'Previous 90 Days', sessions: [] },
    { period: 'Previous 180 Days', sessions: [] },
    { period: 'Older', sessions: [] },
  ]

  const now = dayjsUTC()

  sessions.forEach(session => {
    const latestConversationAt = dayjsUTC(session.latestConversationDatetime)

    if (latestConversationAt.isSame(now, 'day')) {
      groupedSessions.find(g => g.period === 'Today')?.sessions.push(session)
    } else if (latestConversationAt.isSame(now.subtract(1, 'day'), 'day')) {
      groupedSessions
        .find(g => g.period === 'Yesterday')
        ?.sessions.push(session)
    } else if (latestConversationAt.isAfter(now.subtract(7, 'day'))) {
      groupedSessions
        .find(g => g.period === 'Previous 7 Days')
        ?.sessions.push(session)
    } else if (latestConversationAt.isAfter(now.subtract(30, 'day'))) {
      groupedSessions
        .find(g => g.period === 'Previous 30 Days')
        ?.sessions.push(session)
    } else if (latestConversationAt.isAfter(now.subtract(90, 'day'))) {
      groupedSessions
        .find(g => g.period === 'Previous 90 Days')
        ?.sessions.push(session)
    } else if (latestConversationAt.isAfter(now.subtract(180, 'day'))) {
      groupedSessions
        .find(g => g.period === 'Previous 180 Days')
        ?.sessions.push(session)
    } else {
      groupedSessions.find(g => g.period === 'Older')?.sessions.push(session)
    }
  })

  return groupedSessions
}

const parseToFeedbackLabels = (val: string) =>
  val
    ?.split(',')
    .filter(item => !isEmpty(item))
    .map(i => {
      const [label, value] = i.split('|')
      return { label, value }
    })

const Chat: React.FC = () => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false)
  const navigate = useExNavigate()
  const [sessionLoading, setSessionLoading] = useState(true)
  const [channelLoading, setChannelLoading] = useState(false)
  const [contextLoading, setContextLoading] = useState(false)
  const [conversations, setConversations] = useState<ChatConversation[]>([])
  const [dateRange, setDateRange] = useState(dateRangeItems[0])
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [sessionPageInput, setSessionPageInput] = useState('1')
  const [sessionTotalPages, setSessionTotalPages] = useState(1)
  const [sessionPage, setSessionPage] = useState(1)
  const [sessionSort, setSessionSort] = useState(false)
  const [agent, setAgent] = useState<ResponsesAgentResponse>()
  const [searchParams] = useSearchParams()
  const [agentUUID, setAgentUUID] = useState(searchParams.get('agent_uuid'))
  const [sessionUUID, setSessionUUID] = useState(
    searchParams.get('session_uuid'),
  )
  const [workflow, setWorkflow] = useState<ResponsesWorkflowResponse>()
  const [canUploadDocuments, setCanUploadDocuments] = useState(false)
  const [canUploadImages, setCanUploadImages] = useState(false)
  const [fileSizeLimit, setFileSizeLimit] = useState(10 * 1024 * 1024)
  const chatInputRef = useRef<ChatInputRef>(null)
  const [mainChannel, setMainChannel] = useState<ChatMainChannel>()
  const contentRef = useRef<HTMLDivElement>(null)
  const currentSession = useMemo(
    () => sessions.find(s => s.uuid === sessionUUID),
    [sessions, sessionUUID],
  )
  const workflowGeneration = workflow?.workflowGeneration

  useEffect(() => {
    if (!workflow) {
      return
    }
    const nodes = JSON.parse(workflow.nodes)
    const startNode = nodes.find((node: any) => node.id === 'start')
    if (!startNode) {
      return
    }
    const chatToImageInput = startNode.data.data.input.find(
      (input: any) => input.name === 'chat_to_image',
    )
    if (chatToImageInput) {
      setCanUploadImages(chatToImageInput.value)
    }
    const chatToDocInput = startNode.data.data.input.find(
      (input: any) => input.name === 'chat_to_document',
    )
    if (chatToDocInput) {
      setCanUploadDocuments(chatToDocInput.value)
    }
    const fileSizeLimitInput = startNode.data.data.input.find(
      (input: any) => input.name === 'file_size_limit',
    )
    if (fileSizeLimitInput) {
      setFileSizeLimit(fileSizeLimitInput.value)
    }
  }, [workflow])

  useEffect(() => {
    if (!agent) {
      return
    }
    const loadWorkflow = async () => {
      const response = await v1WorkflowGetLatestWorkflowByAgentList({
        agentID: agent.id,
      })
      setWorkflow(response.data)
    }
    loadWorkflow()
  }, [agent])

  useEffect(() => {
    setAgentUUID(searchParams.get('agent_uuid'))
  }, [searchParams.get('agent_uuid')])

  useEffect(() => {
    setSessionUUID(searchParams.get('session_uuid'))
  }, [searchParams.get('session_uuid')])

  // Load agent and channel
  useEffect(() => {
    if (!agentUUID) {
      navigate('/portal/agent')
      return
    }

    const loadChannel = (agentID: number) => {
      setChannelLoading(true)
      v1PublishchannelMainList({ agent_id: agentID })
        .then(res => {
          const data = res.data
          setMainChannel({
            ...data,
            extraData: JSON.parse(data.extraData),
          })
        })
        .finally(() => {
          setChannelLoading(false)
        })
    }

    v1AgentList({ uuid: agentUUID }).then(res => {
      loadChannel(res.data.id)
      setAgent(res.data)
    })
  }, [agentUUID])

  // Load sessions
  useEffect(() => {
    if (!agent) {
      return
    }

    const load = () => {
      setSessionLoading(true)
      v1ChatSessionsList({
        page: sessionPage,
        limit: 30,
        sort: JSON.stringify({
          latest_conversation_datetime: sessionSort ? 'asc' : 'desc',
        }),
        conditions: JSON.stringify([
          { field: 'agent_id', operator: '=', value: agent.id },
          {
            field: 'latest_conversation_datetime',
            operator: '>',
            value: dayjsUTC()
              .utc()
              .subtract(parseInt(dateRange?.key as any), 'day')
              .format(),
          },
        ]),
      })
        .then(res => {
          setSessions(res.data.data)
          setSessionPage(res.data.pagination.currentPage)
          setSessionTotalPages(res.data.pagination.totalPages)
        })
        .finally(() => {
          setSessionLoading(false)
        })
    }

    // Debounce load sessions
    const isInput = ['input', 'button'].includes(
      document?.activeElement?.nodeName.toLowerCase() ?? '',
    )
    const timer = setTimeout(load, isInput ? 300 : 0)
    return () => clearTimeout(timer)
  }, [agent, sessionPage, dateRange, sessionSort])

  // Load conversations
  useEffect(() => {
    setConversations([])

    if (sessionUUID && !channelLoading) {
      setContextLoading(true)
      v1ChatConversationsList({ session_uuid: sessionUUID })
        .then(res => {
          const conversations: ChatConversation[] = []
          res.data.forEach(conversation => {
            if (conversation.documents?.length > 0) {
              conversations.push({
                ...conversation,
                type: 'file',
                hasError: false,
                isLoading: false,
                startTime: '',
                endTime: '',
              })
            }
            conversations.push({
              ...conversation,
              type: 'text',
              hasError: false,
              isLoading: false,
              startTime: '',
              endTime: '',
            })
          })
          setConversations(conversations)
        })
        .finally(() => {
          setContextLoading(false)
          autoScroll()
        })
    }
  }, [sessionUUID, channelLoading])

  const groupedSessions = useMemo<MenuProps['items']>(() => {
    const groups = groupSessions(sessions)
      .filter(group => group.sessions.length > 0)
      .map(group => ({
        ...group,
        sessions: group.sessions.sort((a, b) => {
          const at = dayjsUTC(a.latestConversationDatetime).unix()
          const bt = dayjsUTC(b.latestConversationDatetime).unix()
          return sessionSort ? at - bt : bt - at
        }),
      }))
      .map(group => ({
        key: `g-${group.period}`,
        label: group.period.charAt(0).toUpperCase() + group.period.slice(1),
        type: 'group' as const,
        children: group.sessions.map(session => ({
          key: session.uuid,
          label: (
            <SessionItem
              uuid={session.uuid}
              title={session.title}
              onRename={(uuid, title) =>
                setSessions(
                  sessions.map(s => (s.uuid === uuid ? { ...s, title } : s)),
                )
              }
              onDelete={uuid =>
                setSessions(sessions.filter(s => s.uuid !== uuid))
              }
            />
          ),
        })),
      }))

    return sessionSort ? groups.reverse() : groups
  }, [sessions, sessionSort])

  const onSessionClick: MenuProps['onClick'] = e => {
    const sessionUUID = e.key
    navigate(
      `${window.location.pathname}?agent_uuid=${agentUUID}&session_uuid=${sessionUUID}`,
      { replace: true },
    )
    setSessionUUID(sessionUUID)
  }

  const handleNewSession = () => {
    setSessionUUID('')
    setConversations([])
    navigate(`${window.location.pathname}?agent_uuid=${agentUUID}`, {
      replace: true,
    })
  }

  const send = async (
    agentUUID: string,
    sessionUUID: string,
    content: string,
    fileList: UploadProps<GenieCoreAppHandlersChatResponsesDocumentMetadataResponse>['fileList'] = [],
  ) => {
    const image = fileList.find(file =>
      supportedImageTypes.includes(file.type ?? ''),
    )?.response
    const document = fileList.find(file =>
      supportedDocumentTypes.includes(file.type ?? ''),
    )?.response
    const hasFile = image || document

    const userConversation: ChatConversation = {
      id: '',
      sessionUUID: sessionUUID,
      userID: 0,
      organizationID: 0,
      agentID: 0,
      content,
      type: 'text',
      documents: [],
      audios: [],
      role: ModelsConversationRoleType.RoleUser,
      dislike: false,
      like: false,
      feedback: '',
      feedbackLabel: '',
      createdAt: dayjsUTC().utc().toISOString(),
      intents: [],
      nodeInfos: [],
      session: currentSession,
      responseDurationInMilliseconds: 0,
      userTokens: 0,
      error: '',
      hasError: false,
      isLoading: false,
      startTime: '',
      endTime: '',
    }

    const userFileConversation: ChatConversation = {
      ...userConversation,
      type: 'file',
      documents: document ? [document] : [],
    }

    const assistantConversation: ChatConversation = {
      id: '',
      sessionUUID: sessionUUID,
      userID: 0,
      organizationID: 0,
      agentID: 0,
      content: '',
      type: 'text',
      documents: [],
      audios: [],
      role: ModelsConversationRoleType.RoleAssistant,
      dislike: false,
      like: false,
      feedback: '',
      feedbackLabel: '',
      createdAt: dayjsUTC().utc().toISOString(),
      userTokens: 0,
      intents: [],
      nodeInfos: [],
      session: currentSession,
      responseDurationInMilliseconds: 0,
      error: '',
      hasError: false,
      isLoading: true,
      startTime: '',
      endTime: '',
    }

    const updateConversations = () => {
      setConversations([
        ...conversations,
        ...(hasFile ? [userFileConversation] : []),
        userConversation,
        assistantConversation,
      ])
    }

    updateConversations()

    const params: GenieCoreAppHandlersChatRequestsSendRequest = {
      agentUUID: agentUUID ?? '',
      sessionUUID: sessionUUID,
      language: getUserLanguage(),
      userPrompt: content,
      documentID: document?.id ?? '',
      imageID: image?.id ?? '',
      audioID: '',
      customFields: {},
    }

    const onMessage = (parsedData: SendResponse) => {
      userConversation.id = parsedData.requestMessageID

      if (parsedData.status !== 'completed') {
        assistantConversation.content += parsedData.answer
      }

      assistantConversation.id = parsedData.responseMessageID
      assistantConversation.startTime = parsedData.startTime
      assistantConversation.endTime = parsedData.endTime
      assistantConversation.nodeInfos = parsedData.nodeInfos
      assistantConversation.userTokens = parsedData.completionTokens

      updateConversations()
      autoScroll()

      // Update the latest datetime only when the session list matches
      // any one of the following conditions:
      //
      // 1. total pages <= 1
      // 2. desc sorted and in the 1st page
      // 3. asc sorted and in the last page
      //
      if (
        sessionTotalPages > 1 &&
        ((!sessionSort && sessionPage !== 1) ||
          (sessionSort && sessionPage !== sessionTotalPages))
      ) {
        setSessions([...sessions.filter(s => s.uuid !== sessionUUID)])
        return
      }

      const now = dayjsUTC().utc().toISOString()
      if (currentSession) {
        currentSession.latestConversationDatetime = now
        setSessions([
          ...sessions.filter(s => s.uuid !== sessionUUID),
          currentSession,
        ])
      } else {
        setSessionUUID(parsedData.uuid)

        const session: ChatSession = {
          id: Date.now(),
          uuid: parsedData.uuid,
          title: parsedData.title,
          agentID: parsedData.agentID,
          userID: 0,
          organizationID: 0,
          latestConversationDatetime: now,
          createdAt: now,
          updatedAt: now,
        }
        setSessions([...sessions, session])
      }
    }

    const onError = (error: any) => {
      console.error('Error occurred:', error)
      assistantConversation.hasError = true
      assistantConversation.isLoading = false
    }

    const onComplete = () => {
      assistantConversation.hasError = false
      assistantConversation.isLoading = false
    }

    const timer = setInterval(() => {
      assistantConversation.responseDurationInMilliseconds += 500
      updateConversations()
      autoScroll()
    }, 500)

    try {
      autoScroll()
      chatInputRef.current?.clearFiles()
      /**
       * request and response payload is the same, but backend is different.
       * until usage of v1 is removed, we need call v1 n v2 based on workflowGeneration.
       */
      const requestUrl =
        workflowGeneration === 1 ? apiConfig.chatSend : apiConfig.chatSendV2
      await createStreamingApiRequest(requestUrl, {
        body: params,
        onMessage,
        onError,
        onComplete,
      })
    } catch (error) {
      console.error('Request failed:', error)
    } finally {
      clearInterval(timer)
    }
  }

  const autoScroll = () => {
    setTimeout(() => {
      const element = contentRef.current
      if (!element) return
      const maxScroll = element.scrollHeight - element.clientHeight
      if (element.scrollTop === maxScroll) return
      const scrollStep = (maxScroll - element.scrollTop) / 10
      let lastScrollTop = element.scrollTop
      const animateScroll = () => {
        if (!element) return
        const newScrollTop = Math.min(element.scrollTop + scrollStep, maxScroll)
        element.scrollTop = newScrollTop
        if (newScrollTop < maxScroll && lastScrollTop !== newScrollTop) {
          lastScrollTop = newScrollTop
          requestAnimationFrame(animateScroll)
        }
      }
      requestAnimationFrame(animateScroll)
    }, 200)
  }

  const renderAvatar = (
    avatarUUID: string,
    size: number,
    style?: CSSProperties,
  ) => {
    return (
      <>
        {isEmpty(avatarUUID) ? (
          <Avatar
            shape="square"
            size={size}
            icon={<QuestionCircleOutlined />}
            style={style}
          />
        ) : (
          <Avatar
            shape="square"
            size={size}
            src={getFileUrl(avatarUUID)}
            style={style}
          />
        )}
      </>
    )
  }

  const onSendMessage = async (
    content: string,
    fileList: UploadProps<GenieCoreAppHandlersChatResponsesDocumentMetadataResponse>['fileList'] = [],
  ) => {
    return await send(agentUUID ?? '', sessionUUID ?? '', content, fileList)
  }

  const onAttachFile: UploadProps<GenieCoreAppHandlersChatResponsesDocumentMetadataResponse>['customRequest'] =
    // handle promise internally as antd does not accept async func
    function ({ file, onSuccess, onError, onProgress }) {
      v1ChatConversationsUploadDocumentCreate(
        {
          file: file as File,
          agent_uuid: agentUUID ?? '',
          session_uuid: sessionUUID ?? '',
        },
        {
          onUploadProgress: event => {
            onProgress?.({
              ...event,
              percent: Math.round(
                ((event.loaded * 1.0) / (event.total ?? 0)) * 100,
              ),
            })
          },
        },
      )
        .then(response => {
          const data = response.data
          onSuccess?.(data, file)
        })
        .catch((error: any) => {
          const data: any = {
            status: error.response.status,
            method: error.response.config.method,
            url: error.response.config.url,
          }
          onError?.(data)
        })
    }

  const renderStartPage = (questions: string[]) => {
    return (
      <Flex className={styles.chatStartPage} vertical gap={64}>
        <div className={styles.chatStartPageBackground}></div>
        <Flex className={styles.chatStartPageHeader} vertical gap={48}>
          <Flex className={styles.chatStartPageHeaderAvatar} gap={12}>
            {renderAvatar(agent?.agentIconUUID ?? '', 64, {
              backgroundColor: isEmpty(agent?.agentIconUUID)
                ? '#bbb'
                : undefined,
            })}
            {agent?.agentName}
          </Flex>
          {agent?.description}
        </Flex>
        <Flex
          vertical
          gap={12}
          style={{ display: questions.length > 0 ? undefined : 'none' }}
        >
          You can ask me
          <Flex className={styles.chatStartPageQuestions} vertical gap={4}>
            {React.Children.toArray(
              questions.map((question, index) => (
                <Button onClick={() => onSendMessage(question)}>
                  {question}
                </Button>
              )),
            )}
          </Flex>
        </Flex>
      </Flex>
    )
  }

  const renderConversations = (conversations: ChatConversation[]) => {
    return conversations.map((conversation, index: number) => {
      const avatarUrl =
        conversation.role === 'assistant'
          ? getFileUrl(agent?.agentIconUUID ?? '')
          : getFileUrl(userStore.loginUser?.avatarUUID ?? '')
      const name = conversation.role === 'assistant' ? agent?.agentName : 'You'
      const nodeInfos = conversation.nodeInfos?.map(nodeInfo => ({
        nodeId: nodeInfo.nodeId,
        nodeName: nodeInfo.nodeName,
        nodeType: nodeInfo.nodeType,
        startTime: nodeInfo.startTime,
        endTime: nodeInfo.endTime,
        status: nodeInfo.status,
        logs: nodeInfo.logs
          .map(log => {
            const startTime = dayjsUTC(log.startTime).format('YYMMDDHHmmss.SSS')
            const endTime = log.endTime
              ? dayjsUTC(log.endTime).format('YYMMDDHHmmss.SSS')
              : 'NA'
            const error = log.error ? 'Error: ' + log.error : ''
            return `[${startTime}][${endTime}][${log.status}] ${log.message} ${error}`
          })
          .join('\n'),
        details: JSON.stringify(nodeInfo, null, 4),
        durationInMilliseconds: nodeInfo.durationInMicroSeconds / 1000,
      }))

      const feedbackLikeLabels: FeedbackLabel[] = []
      const feedbackDislikeLabels: FeedbackLabel[] = []

      if (conversation.like === true || conversation.dislike === true) {
        const labels = parseToFeedbackLabels(conversation.feedbackLabel)
        labels.forEach(label => {
          feedbackLikeLabels.push(label)
          feedbackDislikeLabels.push(label)
        })
      } else {
        const likeLabels = parseToFeedbackLabels(
          mainChannel?.extraData?.feedbackOperationLikeOptions ?? '',
        )
        const dislikeLabels = parseToFeedbackLabels(
          mainChannel?.extraData?.feedbackOperationUnlikeOptions ?? '',
        )
        likeLabels.forEach(label => {
          feedbackLikeLabels.push(label)
        })
        dislikeLabels.forEach(label => {
          feedbackDislikeLabels.push(label)
        })
      }

      let feedbackLike: boolean | undefined
      if (conversation.like === true) {
        feedbackLike = true
      } else if (conversation.dislike === true) {
        feedbackLike = false
      }

      const content =
        conversation.type === 'file'
          ? {
              name: conversation.documents[0]?.filename,
              type: conversation.documents[0]?.fileType,
              size: conversation.documents[0]?.fileSize,
            }
          : conversation.content

      return (
        <Message
          key={index}
          id={conversation.id}
          avatarUrl={avatarUrl}
          name={name}
          content={content}
          type={conversation.type}
          role={conversation.role}
          isLoading={conversation.isLoading}
          feedbackLike={feedbackLike}
          userTokens={conversation.userTokens}
          durationInMs={conversation.responseDurationInMilliseconds}
          feedbackLikeLabels={feedbackLikeLabels}
          feedbackDislikeLabels={feedbackDislikeLabels}
          feedbackPlaceholder="Enter a description..."
          nodeInfos={nodeInfos}
          onAction={action => {
            if (action === MessageAction.Retry) {
              const userConversation = conversations[index - 1]
              send(agentUUID ?? '', sessionUUID ?? '', userConversation.content)
            }
          }}
        />
      )
    })
  }

  const renderSessionHistory = () => {
    if (sessionLoading) {
      return (
        <div style={{ margin: '16px' }}>
          <Skeleton active />
        </div>
      )
    }

    if (groupedSessions?.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ margin: 'auto' }}
        />
      )
    }

    return (
      <Menu
        className={styles.leftSectionHistoryList}
        mode="inline"
        items={groupedSessions}
        onClick={onSessionClick}
        selectedKeys={currentSession ? [currentSession.uuid] : []}
      />
    )
  }

  return (
    <>
      <Splitter className={styles.mainContainer}>
        <Splitter.Panel defaultSize={292} min={292} max={820}>
          <Flex className={styles.leftSection} vertical>
            <Flex className={styles.leftSectionHeader}>
              <Link to="/portal/agent">
                <Space>
                  <LeftOutlined />
                  {renderAvatar(agent?.agentIconUUID ?? '', 32)}
                  {agent?.agentName}
                </Space>
              </Link>
            </Flex>
            <Flex className={styles.leftSectionSession} gap={8}>
              <Button
                className={styles.leftSectionSearchButton}
                onClick={() => setIsSearchModalOpen(true)}
              >
                <SearchOutlined />
              </Button>
              <Button
                type="text"
                icon={<PlusOutlined />}
                className={styles.leftSectionSessionNewButton}
                onClick={handleNewSession}
              />
            </Flex>
            <Flex className={styles.leftSectionFilter} gap={8}>
              <Dropdown
                menu={{
                  items: dateRangeItems,
                  onClick: item => {
                    const data = dateRangeItems.find(
                      (i: any) => i.key === item.key,
                    )
                    if (data) {
                      setDateRange(data)
                    }
                  },
                }}
              >
                <Button type="text" className={styles.leftSectionFilterRange}>
                  <CalendarOutlined />
                  {(dateRange as any).label}
                </Button>
              </Dropdown>
              <Button
                icon={
                  sessionSort ? (
                    <SortAscendingOutlined />
                  ) : (
                    <SortDescendingOutlined />
                  )
                }
                onClick={() => {
                  // Set loading to true to prevent showing from debounce time
                  setSessionLoading(true)
                  setSessionSort(!sessionSort)
                }}
                type="text"
              />
            </Flex>

            <Flex className={styles.leftSectionHistory} vertical>
              {renderSessionHistory()}
            </Flex>
            <Flex className={styles.leftSectionNavigation} gap={4}>
              <Button
                onClick={() => {
                  if (sessionPage > 1) {
                    const page = sessionPage - 1
                    setSessionPage(page)
                    setSessionPageInput(page.toString())
                  }
                }}
                disabled={sessionPage === 1}
              >
                ←
              </Button>
              <Space size={8}>
                <Input
                  value={sessionPageInput}
                  onChange={e => {
                    setSessionPageInput(e.target.value)
                    const value = parseInt(e.target.value)
                    if (value > 0 && value <= sessionTotalPages) {
                      setSessionPage(value)
                    }
                  }}
                  onBlur={e => {
                    const value = parseInt(e.target.value)
                    if (value <= 0) {
                      setSessionPage(1)
                      setSessionPageInput('1')
                    } else if (value > sessionTotalPages) {
                      setSessionPage(sessionTotalPages)
                      setSessionPageInput(sessionTotalPages.toString())
                    }
                  }}
                />
                /{sessionTotalPages}
              </Space>
              <Button
                onClick={() => {
                  const page = sessionPage + 1
                  setSessionPage(page)
                  setSessionPageInput(page.toString())
                }}
                disabled={sessionPage === sessionTotalPages}
              >
                →
              </Button>
            </Flex>
          </Flex>
        </Splitter.Panel>
        <Splitter.Panel>
          <Flex className={styles.chatSection} vertical gap={32}>
            <Flex
              className={styles.chatSectionMessageContainer}
              ref={contentRef}
            >
              <Flex className={styles.chatSectionMessageInner} vertical>
                <Flex
                  style={{
                    flex: 1,
                    marginBottom: conversations.length > 0 ? '72px' : undefined,
                  }}
                  vertical
                >
                  {renderStartPage(
                    mainChannel?.extraData?.openingQuestions ?? [],
                  )}
                </Flex>
                {contextLoading ? (
                  <Skeleton active />
                ) : (
                  renderConversations(conversations)
                )}
              </Flex>
            </Flex>
            <Flex className={styles.chatSectionInputContainer}>
              <ChatInput
                ref={chatInputRef}
                placeholder="Shift + Enter line feed; You can copy and paste/drag and drop uploaded documents or images"
                onAttachFile={onAttachFile}
                onSendMessage={onSendMessage}
                canUpload={canUploadDocuments || canUploadImages}
                supportFileTypes={[
                  ...(canUploadImages ? supportedImageTypes : []),
                  ...(canUploadDocuments ? supportedDocumentTypes : []),
                ]}
                fileSizeLimit={fileSizeLimit}
                maxFilesCount={1}
                shouldFileWithMessage
              />
            </Flex>
          </Flex>
        </Splitter.Panel>
      </Splitter>
      <SearchModal
        open={isSearchModalOpen}
        okText="Close"
        data={{
          agentId: agent?.id || 0,
          agentName: agent?.agentName || '',
          agentIconUUID: agent?.agentIconUUID || '',
        }}
        onFinish={() => {}}
        onCancel={() => setIsSearchModalOpen(false)}
      />
    </>
  )
}

export default observer(Chat)
