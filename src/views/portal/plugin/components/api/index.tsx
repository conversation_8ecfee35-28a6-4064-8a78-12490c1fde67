import { Button, Col, Form, Input, Select, Tabs } from 'antd'
import { memo, useMemo } from 'react'
import StickyBox from 'react-sticky-box'

import { Method } from 'axios'
import Header from './header'
import styles from './index.scss'
import Parameters from './parameters'

import type { FormInstance, TabsProps } from 'antd'
import { FormItemType } from 'components/new-dynamic-form/types'
import { isLocal } from 'utils/env'
import Body from './body'
import { ApiFormsContext } from './context'
import { TableSectionProps } from './table-section'

const options: Array<{ label: Method; value: Method }> = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'PUT', value: 'PUT' },
]

type ApiProps = {
  configurationForm?: FormItemType
  form: FormInstance
  forms: FormItemType[]
  curFormId: string
  formBasename?: string
  mode?: TableSectionProps['mode']
  onSendClick?: () => void
}

const Api = (props: ApiProps) => {
  const {
    configurationForm,
    form,
    forms,
    formBasename = '',
    mode = 'default',
    onSendClick,
  } = props

  const handleTestClick = () => {
    console.error(form.getFieldsValue())
  }

  const defaultTabItems: TabsProps['items'] = [
    {
      key: 'Parameters',
      label: 'Parameters',
      children: (
        <Parameters
          form={form}
          formBasename={formBasename}
          key={formBasename}
        />
      ),
    },
    {
      key: 'Header',
      label: 'Header',
      children: (
        <Header form={form} formBasename={formBasename} key={formBasename} />
      ),
    },
    {
      key: 'Body',
      label: 'Body',
      children: (
        <Body form={form} formBasename={formBasename} key={formBasename} />
      ),
    },
  ]

  const contextValue = useMemo(
    () => ({ configurationForm, forms, mode }),
    [configurationForm, forms, mode],
  )

  return (
    <ApiFormsContext.Provider value={contextValue}>
      <div className={styles.apiWrapper}>
        <div className={styles.apiHeaderWrapper}>
          <Col flex={1}>
            <Form.Item
              name={[formBasename, 'url', 'path']}
              className={styles.apiHeaderInput}
            >
              <Input
                addonBefore={
                  <Form.Item
                    name={[formBasename, 'url', 'method']}
                    noStyle
                    initialValue="GET"
                  >
                    <Select options={options} popupMatchSelectWidth={false} />
                  </Form.Item>
                }
                disabled={mode === 'test'}
              />
            </Form.Item>
          </Col>
          {mode === 'test' && onSendClick && (
            <Button type="primary" onClick={onSendClick} disabled={false}>
              Send
            </Button>
          )}
          {isLocal && <Button onClick={handleTestClick}>Local Test</Button>}
        </div>

        <Tabs
          items={defaultTabItems}
          defaultActiveKey={defaultTabItems[0].key}
          renderTabBar={(props, DefaultTabBar) => (
            <StickyBox offsetTop={0} className={styles.apiContentTabBarWrapper}>
              <DefaultTabBar {...props} />
            </StickyBox>
          )}
        />
      </div>
    </ApiFormsContext.Provider>
  )
}

export default memo(Api)
