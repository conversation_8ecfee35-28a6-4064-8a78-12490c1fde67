import { Form, FormInstance, TreeSelectProps } from 'antd'
import { TableRowSelection } from 'antd/es/table/interface'
import { merge } from 'lodash-es'
import { memo, useEffect, useMemo } from 'react'

import {
  PluginPluginDataType,
  PluginPluginInputPayloadType,
} from 'api/data-contracts'
import { convertFormItemsToTreeData } from 'components/new-dynamic-form/util'
import { useApiFormsContext } from 'views/portal/plugin/components/api/context'
import TableSection, { DataType, getDefaultRecord } from '../table-section'

type HeaderProps = {
  form: FormInstance
  formBasename?: string
}

const Header = (props: HeaderProps) => {
  const { form, formBasename = '' } = props
  const { configurationForm, forms, mode } = useApiFormsContext() ?? {}

  const referenceTreeData = useMemo<TreeSelectProps['treeData']>(() => {
    const curForm = forms?.find(f => f.formItemId === formBasename)
    if (!curForm) return

    const configurationData = convertFormItemsToTreeData(
      configurationForm?.children ?? [],
      { pathPrefix: 'configuration' },
    )
    const propertyData = convertFormItemsToTreeData(curForm.children, {
      pathPrefix: 'property',
    })

    return [
      {
        title: 'Configuration Form',
        value: 'configuration_form',
        children: configurationData,
        disabled: true,
      },
      {
        title: 'Property Form',
        value: 'property_form',
        children: propertyData,
        disabled: true,
      },
    ]
  }, [])

  const baseNamePath = useMemo(() => [formBasename, 'header'], [formBasename])
  const headerDataTableData = Form.useWatch<DataType[]>(baseNamePath, {
    preserve: true,
  })

  const selectedRowKeys = useMemo(
    () => headerDataTableData?.filter(i => i.selected).map(i => i.id),
    [headerDataTableData],
  )

  const onRecordChange = (
    namePath: string[],
    key: string,
    value: any,
    record: DataType,
    index: number,
  ) => {
    const newData = [...headerDataTableData]
    const newRecord: DataType = { ...record }

    if (index === newData?.length - 1) {
      merge(newRecord, {
        selected: true,
        dataType: PluginPluginDataType.String,
        type: PluginPluginInputPayloadType.PluginInputPayloadTypeInput,
      })

      newData.push(getDefaultRecord({ selected: false }))
    }

    if (['dataType', 'type'].includes(key)) {
      Reflect.set(newRecord, 'value', '')
      // Reflect.set(newRecord, 'reference', '')
    }

    Reflect.set(newRecord, key, value)

    // if (
    //   record.type ===
    //   PluginPluginInputPayloadType.PluginInputPayloadTypeReference
    // ) {
    //   Reflect.set(newRecord, 'reference', value)
    // } else {
    //   Reflect.set(newRecord, 'reference', '')
    // }

    newData.splice(index, 1, newRecord)
    form.setFieldValue(baseNamePath, newData)
  }

  const handleDelete = (record: DataType) => {
    const newData = headerDataTableData?.filter(v => v.id !== record.id)
    form.setFieldValue(baseNamePath, newData)
  }

  const handleRowSelectChange: TableRowSelection<DataType>['onChange'] =
    selectedRowKeys => {
      const newData = [...headerDataTableData].map(i => ({
        ...i,
        selected: selectedRowKeys.includes(i.id),
      }))

      form.setFieldValue(baseNamePath, newData)
    }

  useEffect(() => {
    if (headerDataTableData && !headerDataTableData.length) {
      form.setFieldValue(baseNamePath, [getDefaultRecord({ selected: false })])
    }
  }, [headerDataTableData])

  return (
    <div>
      <TableSection<DataType>
        namePath={baseNamePath}
        onCellChange={onRecordChange}
        dataSource={headerDataTableData}
        onDelete={handleDelete}
        tableProps={{
          rowSelection: {
            selectedRowKeys,
            onChange: handleRowSelectChange,
          },
        }}
        referenceTreeData={referenceTreeData}
        mode={mode}
      />
    </div>
  )
}

export default memo(Header)
