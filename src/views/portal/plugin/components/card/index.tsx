import { DeleteOutlined, FormOutlined } from '@ant-design/icons'
import { Tag, Typography, type MenuProps } from 'antd'
import dayjs from 'dayjs'
import { memo } from 'react'
import { useNavigate } from 'react-router-dom'

import CustomCard from 'components/custom-card'
import { setQuery } from 'utils/common'

import { v1PluginsDelete } from 'api/Api'
import { type ResponsesPluginWithLatestFormsOfAllStatusesResponse } from 'api/data-contracts'
import PluginColor from 'assets/images/plugin-color.svg'
import CustomAvatar from 'components/custom-avatar'
import { capitalizeFirstLetter } from 'utils/format'

type PluginCardProps = {
  data: ResponsesPluginWithLatestFormsOfAllStatusesResponse
  onUpdate?: (...args: any[]) => void
}

const { Paragraph } = Typography
const ActionMenuItem: MenuProps['items'] = [
  { key: 'edit', label: 'Edit Info', icon: <FormOutlined /> },
  { key: 'delete', label: 'Delete', icon: <DeleteOutlined /> },
]

const PluginCard = (props: PluginCardProps) => {
  const { data, onUpdate } = props
  const { iconUUID, name, description, id } = data
  const updatedAt = '2023-08-01T12:00:00Z'
  const navigate = useNavigate()

  const handleCardClick = () => {
    const search = setQuery({ opType: 'view' })
    // Check if we're in the marketplace route by examining the current URL
    const isMarketplace = window.location.pathname.includes('/marketplace/')
    if (isMarketplace) {
      navigate(`/portal/marketplace/my-plugins/details/${id}?${search}`)
    } else {
      navigate(`/portal/plugin/detail/${id}?${search}`)
    }
  }

  const handleMenuItemClick: MenuProps['onClick'] = info => {
    const { key } = info
    if (key === 'edit') {
      const search = setQuery({ opType: 'edit' })
      // Check if we're in the marketplace route by examining the current URL
      const isMarketplace = window.location.pathname.includes('/marketplace/')
      if (isMarketplace) {
        navigate(`/portal/marketplace/my-plugins/details/${id}?${search}`)
      } else {
        navigate(`/portal/plugin/detail/${id}?${search}`)
      }
    } else if (key === 'delete') {
      v1PluginsDelete(id).then(() => {
        onUpdate?.({})
      })
    }
  }

  return (
    <CustomCard onClick={handleCardClick}>
      <CustomCard.Header
        title={name}
        subTitle={`Update time ${dayjs(updatedAt).format('YYYY-MM-DD HH:mm:ss')}`}
        icon={
          <CustomAvatar
            iconUUID={iconUUID}
            shape="square"
            size={54}
            defaultIcon={<PluginColor fontSize={36} />}
          />
        }
      />
      <CustomCard.Content>{description}</CustomCard.Content>
      <CustomCard.Footer
        tags={
          <Tag color="blue">
            {capitalizeFirstLetter(data.latestPublished?.status)}
          </Tag>
        }
        dropdownItems={ActionMenuItem}
        handleMenuClick={handleMenuItemClick}
      />
    </CustomCard>
  )
}

export default memo(PluginCard)
