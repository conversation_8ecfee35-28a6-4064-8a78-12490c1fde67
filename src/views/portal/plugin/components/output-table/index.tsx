import { MinusOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Input,
  Select,
  Space,
  Switch,
  Table,
  TableColumnsType,
} from 'antd'
import { PluginPluginDataType } from 'api/data-contracts'
import { memo, useEffect } from 'react'
import type { OutputDataType } from '../output'
import styles from './index.scss'

type OutputTableProps = {
  // form: FormInstance
  // baseNamePath: (string | number)[]
  // structure: OutputDataType[]
  // setStructure: (data: OutputDataType[]) => void
  data: OutputDataType[]
  onChange: (data: OutputDataType[]) => void
}

const TypeSelectOptionList = [
  ...Object.values(PluginPluginDataType),
  ...Object.values(PluginPluginDataType).map(i => `Array<${i}>`),
]

export const getDefaultOutputValue = (
  defaultProps?: Partial<OutputDataType>,
): OutputDataType => ({
  key: crypto.randomUUID(),
  name: '',
  required: false,
  type: PluginPluginDataType.String,
  deep: 1,
  ...defaultProps,
})

const updateNode = (
  nodes: OutputDataType[],
  opType: 'add' | 'delete' | 'select' | 'input',
  key: string,
  col?: string,
  value?: any,
  parentNode?: OutputDataType,
) => {
  nodes.forEach((node, index) => {
    if (node.key === key) {
      switch (opType) {
        case 'add': {
          const defaultVal = getDefaultOutputValue()
          if (!node.children) Reflect.set(node, 'children', [])
          node.children?.push({ ...defaultVal, deep: node.deep + 1 })
          break
        }
        case 'delete': {
          nodes.splice(index, 1)
          if (!nodes.length && parentNode)
            Reflect.set(parentNode, 'children', undefined)
          break
        }
        case 'input': {
          if (!col) break
          Reflect.set(node, col, value)
          break
        }
        case 'select': {
          node.type = value
          if (
            node.type === PluginPluginDataType.Object ||
            /^Array<.*?>$/.test(node.type)
          ) {
            node.children = []
          } else {
            node.children = undefined
          }
          break
        }
      }
    } else if (node.children?.length) {
      updateNode(node.children, opType, key, col, value, node)
      return
    }
  })
}

export const OutputTable = memo((props: OutputTableProps) => {
  const { data, onChange } = props

  const handleCellChange = (
    col: string,
    val: any,
    record: OutputDataType,
    idx: number,
  ) => {
    const newTreeData = [...data]
    if (newTreeData?.length === idx + 1) {
      newTreeData.push(getDefaultOutputValue())
    }

    if (['type'].includes(col)) {
      updateNode(newTreeData, 'select', record.key, col, val)
    }

    if (['name', 'required'].includes(col)) {
      updateNode(newTreeData, 'input', record.key, col, val)
    }

    onChange?.(newTreeData)
  }

  const handleAddNodeItem = (key: string) => {
    const newTreeData = [...data]
    updateNode(newTreeData, 'add', key)
    onChange?.(newTreeData)
  }

  const handleDeleteNodeItem = (key: string) => {
    const newTreeData = [...data].filter(r => key !== r.key)
    updateNode(newTreeData, 'delete', key)
    onChange?.(newTreeData)
  }

  const columns: TableColumnsType<OutputDataType> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (val, record, idx) => (
        <Input
          variant={'borderless'}
          defaultValue={val}
          onChange={e => handleCellChange('name', e.target.value, record, idx)}
        />
      ),
    },
    {
      title: 'Required',
      dataIndex: 'required',
      key: 'required',
      render: (val, record, idx) => (
        <Switch
          defaultChecked={val}
          onChange={checked =>
            handleCellChange('required', checked, record, idx)
          }
        />
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (val, record, idx) => (
        <Select
          className={styles.outputTableType}
          options={TypeSelectOptionList.map(i => ({ label: i, value: i }))}
          variant="borderless"
          defaultValue={val ?? PluginPluginDataType.String}
          onChange={val => handleCellChange('type', val, record, idx)}
          popupMatchSelectWidth={false}
        />
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (val, record, idx) => (
        <Space.Compact>
          {(record.type === PluginPluginDataType.Object ||
            /^Array<.*?>$/.test(record.type)) && (
            <Button onClick={() => handleAddNodeItem(record.key)}>
              <PlusOutlined />
            </Button>
          )}
          {idx !== data.length - 1 && (
            <Button onClick={() => handleDeleteNodeItem(record.key)}>
              <MinusOutlined />
            </Button>
          )}
        </Space.Compact>
      ),
    },
  ]

  useEffect(() => {
    if (!data.length) {
      onChange?.([getDefaultOutputValue()])
    }

    if (data[data.length - 1]?.name) {
      data.push(getDefaultOutputValue())
      onChange?.(data)
    }
  }, [])

  return (
    <Table<OutputDataType>
      size="small"
      className={styles.outputTable}
      columns={columns}
      dataSource={data}
      pagination={false}
    />
  )
})
