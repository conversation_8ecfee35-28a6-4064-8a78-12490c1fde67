import { But<PERSON>, Splitter } from 'antd'
import cls from 'classnames'
import { cloneDeep } from 'lodash-es'
import { forwardRef, memo, useEffect, useMemo, useRef, useState } from 'react'

import { WithDraggingProps } from 'components/new-dynamic-form/components/lib/component-wrapper'
import { FormContext } from 'components/new-dynamic-form/context'
import FormItemConfig from 'components/new-dynamic-form/form-item-config'
import FormItemRender from 'components/new-dynamic-form/form-item-render'
import FormItemSelector from 'components/new-dynamic-form/form-item-selector'
import { useDropComponents } from 'components/new-dynamic-form/hook'
import { FormItemType } from 'components/new-dynamic-form/types'
import {
  getItemByPath,
  updateItemByPath,
} from 'components/new-dynamic-form/util'

import { isLocal } from 'utils/env'
import OperationBar from '../components/operation-bar'
import styles from './index.scss'

type ConfigurationFormProps = {
  treeRoot: FormItemType
  setTreeRoot: (node: FormItemType) => void
  wrapperClassName?: string
  renderWrapperClassName?: string
}

const ConfigurationForm = forwardRef((props: ConfigurationFormProps, ref) => {
  const { wrapperClassName, renderWrapperClassName, treeRoot, setTreeRoot } =
    props
  const [currentItem, setCurrentItem] = useState<FormItemType | null>(null)
  const rootRef = useRef<HTMLDivElement>(null)

  const handleItemUpdate = (item: WithDraggingProps<FormItemType>) => {
    const { path } = item
    const newRoot = cloneDeep(treeRoot)
    const newItem = updateItemByPath(newRoot, path, item)

    setTreeRoot(newItem)
  }

  useDropComponents(rootRef, treeRoot, setTreeRoot)

  useEffect(() => {
    if (!treeRoot) return
    if (!currentItem) {
      setCurrentItem(treeRoot.children[0])
      return
    }
    const newCurItem = getItemByPath(treeRoot, currentItem?.path)
    if (!newCurItem) return
    setCurrentItem(newCurItem)
  }, [treeRoot])

  const contextValue = useMemo(
    () => ({
      root: treeRoot,
      setRoot: setTreeRoot,
      currentItem,
      onItemClick: setCurrentItem,
    }),
    [treeRoot, setTreeRoot, currentItem, setCurrentItem],
  )

  return (
    <FormContext.Provider value={contextValue}>
      <Splitter
        className={cls(styles.configurationFormWrapper, wrapperClassName)}
      >
        <Splitter.Panel defaultSize={300} min={`15%`} max={432}>
          <FormItemSelector />
        </Splitter.Panel>

        <Splitter.Panel>
          <div
            ref={rootRef}
            className={styles.configurationFormItemRenderWrapper}
          >
            <FormItemRender
              data={treeRoot.children}
              wrapperClassName={cls(
                styles.configurationFormItemRender,
                renderWrapperClassName,
              )}
            />

            <OperationBar
              wrapperClassName={styles.configurationFormOperationBar}
            />
            {isLocal && (
              <Button
                onClick={() => console.error(treeRoot)}
                style={{ position: 'fixed', bottom: '0px', right: '0px' }}
              >
                Local
              </Button>
            )}
          </div>
        </Splitter.Panel>

        <Splitter.Panel collapsible defaultSize={400} min={`15%`} max={600}>
          <FormItemConfig
            key={currentItem?.formItemId}
            data={currentItem}
            onUpdate={handleItemUpdate}
          />
        </Splitter.Panel>
      </Splitter>
    </FormContext.Provider>
  )
})

export default memo(ConfigurationForm)
