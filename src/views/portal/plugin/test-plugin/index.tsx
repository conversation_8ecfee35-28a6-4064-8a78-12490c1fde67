import { Form, Menu, MenuProps, Splitter } from 'antd'
import { useForm } from 'antd/es/form/Form'
import cls from 'classnames'
import { forwardRef, memo, useEffect, useState } from 'react'
import Api from 'views/portal/plugin/components/api'

import { v1PluginFormsThirdPartyEndpointExecuteCreate } from 'api/Api'
import { AxiosResponse } from 'axios'
import { capitalize, cloneDeep, set } from 'lodash-es'
import ResponseBody from 'views/components/response-body'
import { DataType } from '../components/api/table-section'
import OperationBar from '../components/operation-bar'
import { FormItemDetail } from '../endpoint'
import tagStyle from '../endpoint/index.scss'
import styles from './index.scss'

type EndpointProps = {
  forms?: FormItemDetail[]
  wrapperClassName?: string
}

const TestPlugin = forwardRef<unknown, EndpointProps>(
  (props: EndpointProps, ref) => {
    const { forms, wrapperClassName } = props
    const [mainForm] = useForm()
    const [curFormId, setCurFormId] = useState<string>('')
    const [endpointResp, setEndpointResp] =
      useState<AxiosResponse<unknown, any>>()

    const handleSubmit = async () => {
      const curEndpoint: FormItemDetail['endpoint'] = Reflect.get(
        mainForm.getFieldsValue(true),
        curFormId,
      )

      console.error('curEndpoint', curEndpoint)

      const resp = await v1PluginFormsThirdPartyEndpointExecuteCreate({
        method: curEndpoint?.url?.method,
        url: curEndpoint?.url?.path,
        header: curEndpoint.header?.reduce((pre, cur) => {
          if (!cur.value || !cur.selected) return pre
          Reflect.set(pre, cur.key, cur.value)
          return pre
        }, {}),
        query: curEndpoint.query?.reduce((pre, cur) => {
          if (!cur.value || !cur.selected) return pre
          Reflect.set(pre, cur.key, cur.value)
          return pre
        }, {}),
        body: curEndpoint.body?.payload.length
          ? curEndpoint.body?.payload?.reduce((pre, cur) => {
              if (!cur.value || !cur.selected) return pre
              Reflect.set(pre, cur.key, cur.value)
              return pre
            }, {})
          : undefined,
        bodyType: curEndpoint.body?.type ? curEndpoint.body?.type : undefined,
      })
      setEndpointResp(resp)
    }

    const handleMenuItemClick: MenuProps['onClick'] = ({ key }) => {
      setCurFormId(key)
    }

    const handleApiSendClick = () => {
      return handleSubmit()
    }

    useEffect(() => {
      if (!forms?.length) return

      setCurFormId(forms[0].structure.formItemId)
      mainForm.setFieldsValue(
        forms.reduce((pre, cur) => {
          const curEndpoint = cloneDeep(cur.endpoint)
          if (!curEndpoint) return pre
          Object.keys(curEndpoint).forEach(key => {
            let v: DataType[] | undefined
            if (key === 'body') {
              v = Reflect.get(curEndpoint, key)?.payload
              set(
                curEndpoint,
                `${key}.payload`,
                v?.map(i => ({ ...i, value: '', reference: '' })),
              )
            } else if (key === 'header' || key === 'query') {
              v = Reflect.get(curEndpoint, key)
              set(
                curEndpoint,
                key,
                v?.map(i => ({ ...i, value: '', reference: '' })),
              )
            }
          })

          Reflect.set(pre, cur.structure.formItemId, curEndpoint)
          return pre
        }, {}),
      )
    }, [forms])

    return (
      <Form
        form={mainForm}
        className={cls(styles.testPluginWrapper, wrapperClassName)}
        preserve
      >
        <Splitter>
          <Splitter.Panel defaultSize="290" min="10%" max="15%" collapsible>
            <div className={styles.testPluginSiderWrapper}>
              <Menu
                selectedKeys={[curFormId]}
                items={forms?.map(form => {
                  const { name, structure, endpoint } = form
                  return {
                    key: structure.formItemId,
                    label: (
                      <div className={styles.testPluginLabelWrapper}>
                        <span
                          className={cls({
                            [styles.testPluginLabelTag]: true,
                            [tagStyle[
                              `tag${capitalize(endpoint?.url?.method)}`
                            ]]: true,
                          })}
                        >
                          {endpoint?.url.method}
                        </span>
                        <span className={styles.testPluginLabelName}>
                          {name}
                        </span>
                      </div>
                    ),
                  }
                })}
                onClick={handleMenuItemClick}
              />
            </div>
          </Splitter.Panel>

          <Splitter.Panel>
            {curFormId && forms && (
              <Form.Item noStyle name={curFormId}>
                <Splitter layout="vertical">
                  <Splitter.Panel defaultSize={'50%'}>
                    <Api
                      form={mainForm}
                      forms={forms?.map(f => f.structure)}
                      curFormId={curFormId}
                      formBasename={curFormId}
                      onSendClick={handleApiSendClick}
                      mode="test"
                    />
                  </Splitter.Panel>

                  <Splitter.Panel
                    defaultSize={'50%'}
                    className={styles.testPluginResponseWrapper}
                  >
                    <ResponseBody
                      response={endpointResp}
                      onReload={handleApiSendClick}
                      syntaxHighlighterProps={{
                        wrapLines: true,
                        wrapLongLines: true,
                      }}
                      wrapperClassName={styles.testPluginResponseBody}
                    />

                    <OperationBar
                      wrapperClassName={styles.testPluginOperationWrapper}
                    />
                  </Splitter.Panel>
                </Splitter>
              </Form.Item>
            )}
          </Splitter.Panel>
        </Splitter>
      </Form>
    )
  },
)

export default memo(TestPlugin)
