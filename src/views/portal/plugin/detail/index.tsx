import { Avatar, FormInstance, Steps, Tabs } from 'antd'
import cls from 'classnames'
import StaticFunction from 'components/antd-static-function'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

import {
  v1PluginFormsPublishCreate,
  v1PluginFormsSaveCreate,
  v1PluginsDetail,
  v1PluginsPluginFormsLatestDetail,
  v1PluginsUpdate,
  v1PluginWithFormCreate,
} from 'api/Api'
import {
  ResponsesPluginFormResponse,
  ResponsesPluginResponse,
} from 'api/data-contracts'
import BackspaceSvg from 'assets/images/backspace.svg'
import { getRootDefaultValue } from 'components/new-dynamic-form'
import dayjs from 'dayjs'
import Loading from 'layouts/loading'
import { getFileUrl, getQuery, setQuery } from 'utils/common'
import PluginBasicInfo, { PluginInformationFormType } from '../basic-info'
import Endpoint, { FormItemDetail, getDefaultEndpointValue } from '../endpoint'
import WorkflowForm from '../workflow-form'
import styles from './index.scss'

import { useWatch } from 'antd/es/form/Form'
import ConfigurationForm from '../configuration-form'
import TestPlugin from '../test-plugin'

import type { FormItemType } from 'components/new-dynamic-form/types'
import { PluginTabItems } from './constants'
import { OperationContext } from './context'

export type PluginDetailPageParameters = {
  opType: 'edit' | 'create' | 'view'
  curTab: string
}

type PluginDetailType = {
  baseInfo: ResponsesPluginResponse
  formInfo: ResponsesPluginFormResponse
}

const PluginDetail = () => {
  const { message } = StaticFunction
  const navigate = useNavigate()
  const { opType, curTab: defaultCurTab = 'basicInfo' } =
    getQuery<PluginDetailPageParameters>(location.search)
  const { pluginId } = useParams()
  const [loading, setLoading] = useState(false)
  const [curTab, setCurTab] = useState(defaultCurTab)
  const [configTreeData, setConfigTreeData] = useState<FormItemType>()
  const [workflowTreeData, setWorkflowTreeData] = useState<FormItemType[]>()
  const [pluginDetail, setPluginDetail] = useState<PluginDetailType>()
  const [endpoints, setEndpoints] = useState<FormItemDetail[]>([])

  const basicInfoRef = useRef<{
    form: FormInstance<PluginInformationFormType>
  }>(null)
  const endpointRef = useRef<{
    form: FormInstance<Record<string, FormItemDetail['endpoint']>>
  }>(null)

  const finalEndpoints =
    useWatch([], {
      form: endpointRef.current?.form,
      preserve: true,
    }) ?? []

  const initPage = async () => {
    if (opType === 'create') {
      setConfigTreeData(getRootDefaultValue())

      const workflowDefaultVal = [
        getRootDefaultValue({ extends: { name: `Feature 1` } }),
      ]
      setWorkflowTreeData(workflowDefaultVal)

      return
    }
    setLoading(true)

    const [baseInfoResp, detailResp] = await Promise.all([
      v1PluginsDetail(Number(pluginId)),
      v1PluginsPluginFormsLatestDetail(Number(pluginId)),
    ]).finally(() => setLoading(false))

    setPluginDetail({
      baseInfo: baseInfoResp.data,
      formInfo: detailResp.data,
    })

    setConfigTreeData(
      (detailResp.data?.configurationForm as any) ?? getRootDefaultValue(),
    )

    setWorkflowTreeData(
      (detailResp.data?.propertyAndEndpointForms.map(
        form => form.structure,
      ) as any) ?? [getRootDefaultValue()],
    )

    setEndpoints(detailResp.data?.propertyAndEndpointForms as any)
  }

  const handleBackspaceClick = () => {
    navigate(`/portal/marketplace/my-plugins`)
  }

  const handleNextClick = async () => {
    if (curTab === PluginTabItems[0].key) {
      const isValid = await basicInfoRef.current?.form.validateFields()
      if (!isValid) return
    }

    const curIdx = PluginTabItems.findIndex(t => t.key === curTab)
    const nextTab = Reflect.get(PluginTabItems, curIdx + 1)
    setCurTab(nextTab?.key)
  }

  const handlePreviousClick = () => {
    const curIdx = PluginTabItems.findIndex(t => t.key === curTab)
    const nextTab = Reflect.get(PluginTabItems, curIdx - 1)
    setCurTab(nextTab?.key)
  }

  const handleSaveClick = async () => {
    if (basicInfoRef.current) {
      await basicInfoRef.current?.form.validateFields()
    }
    const basicInfo = basicInfoRef.current?.form.getFieldsValue()
    const originEndpoints = endpointRef.current?.form.getFieldsValue(true)
    const finalEndpoints = Object.entries<FormItemDetail['endpoint']>(
      originEndpoints,
    ).reduce<Record<string, FormItemDetail['endpoint']>>((pre, cur) => {
      const [k, v] = cur
      const finalVal = {
        ...v,
        header: v?.header?.filter(i => i.key),
        query: v?.query?.filter(i => i.key),
        body: { ...v?.body, payload: v?.body?.payload.filter(i => i.key) },
        output: {
          ...v?.output,
          structure: v.output?.structure?.filter(i => i.name),
        },
      }
      Reflect.set(pre, k, finalVal)
      return pre
    }, {})

    if (!basicInfo || !finalEndpoints || !workflowTreeData) return
    if (opType === 'create') {
      // base info
      const createResp = await v1PluginWithFormCreate({
        ...basicInfo.pluginInformation,
        iconID: basicInfo.pluginInformation.iconList?.[0]?.response?.[0]?.id,
        configurationForm: configTreeData as any,
        propertyAndEndpointForms: (workflowTreeData?.map(form => ({
          name: form.extends?.name ?? '',
          endpoint: Reflect.get(finalEndpoints, form.formItemId),
          structure: form,
        })) ?? []) as any,
      })

      if (!createResp) return
      message.success('Create Successful!')
      handleBackspaceClick()
    } else {
      if (!pluginDetail) return
      if (!Number(pluginId)) return

      // update base info
      await v1PluginsUpdate(Number(pluginId), {
        ...basicInfo.pluginInformation,
        iconID:
          basicInfo?.pluginInformation?.iconList?.[0]?.response?.[0]?.id ??
          pluginDetail.baseInfo?.iconID,
      })
      // update form
      if (pluginDetail.formInfo) {
        await v1PluginFormsSaveCreate(pluginDetail.formInfo?.id, {
          configurationForm: configTreeData as any,
          propertyAndEndpointForms: (workflowTreeData?.map(form => ({
            name: form.extends?.name ?? '',
            endpoint: Reflect.get(finalEndpoints, form.formItemId),
            structure: form,
          })) ?? []) as any,
        })
      }
      message.success('Update Successful!')
    }
  }

  const handlePublishClick = async () => {
    if (!pluginDetail) return
    const finalEndpoints = Object.entries<FormItemDetail['endpoint']>(
      endpointRef.current?.form.getFieldsValue(true),
    ).reduce<Record<string, FormItemDetail['endpoint']>>((pre, cur) => {
      const [k, v] = cur
      const finalVal = {
        ...v,
        header: v?.header?.filter(i => i.key),
        query: v?.query?.filter(i => i.key),
        body: { ...v?.body, payload: v?.body?.payload.filter(i => i.key) },
        output: {
          ...v?.output,
          structure: v.output?.structure?.filter(i => i.name),
        },
      }
      Reflect.set(pre, k, finalVal)
      return pre
    }, {})
    const publishResp = await v1PluginFormsPublishCreate(
      pluginDetail.formInfo?.id,
      {
        configurationForm: configTreeData as any,
        propertyAndEndpointForms: (workflowTreeData?.map(form => ({
          name: form.extends?.name ?? '',
          endpoint: Reflect.get(finalEndpoints, form.formItemId),
          structure: form,
        })) ?? []) as any,
      },
    ).catch(() => message.error('Publish Failure!'))
    if (!publishResp) return
    message.success('Publish Successful!')
  }

  useEffect(() => {
    initPage()
  }, [])

  useEffect(() => {
    const search = getQuery(location.search)
    const params = setQuery({ ...search, curTab })
    navigate({ search: params })
  }, [curTab])

  useEffect(() => {
    // set default values
    basicInfoRef.current?.form.setFieldsValue({
      pluginInformation: pluginDetail?.baseInfo,
    })

    const defaultEndpoints =
      pluginDetail?.formInfo?.propertyAndEndpointForms?.reduce<
        Record<string, FormItemDetail['endpoint']>
      >((pre, cur) => {
        Reflect.set(pre, cur.structure.formItemId, cur.endpoint)
        return pre
      }, {})
    if (defaultEndpoints)
      endpointRef.current?.form.setFieldsValue(defaultEndpoints)
  }, [pluginDetail])

  useEffect(() => {
    if (!workflowTreeData) return

    setEndpoints(
      workflowTreeData.map(i => {
        const curEndpoint = endpoints.find(
          e => e.structure.formItemId === i.formItemId,
        )
        return {
          name: i.extends?.name,
          structure: i,
          endpoint: curEndpoint?.endpoint ?? getDefaultEndpointValue(),
        }
      }),
    )
  }, [workflowTreeData])

  const operationContextValue = useMemo(
    () => ({
      next: handleNextClick,
      previous: handlePreviousClick,
      save: handleSaveClick,
      publish: handlePublishClick,
    }),
    [handleNextClick, handlePreviousClick, handleSaveClick, handlePublishClick],
  )

  return (
    <OperationContext.Provider value={operationContextValue}>
      <div className={styles.pluginDetailWrapper}>
        <div className={styles.pluginDetailHeaderWrapper}>
          <div className={styles.pluginDetailHeaderLeft}>
            <BackspaceSvg
              className={styles.pluginDetailHeaderLeftBackIcon}
              onClick={handleBackspaceClick}
            />
            <Avatar
              className={styles.pluginDetailHeaderLeftAvatar}
              shape="square"
              src={getFileUrl(pluginDetail?.baseInfo?.icon?.uuid ?? '')}
              draggable={false}
              size={36}
            />
            <div className={styles.pluginDetailHeaderLeftInfoWrapper}>
              <div className={styles.pluginDetailHeaderLeftInfoName}>
                {pluginDetail?.baseInfo?.name}
              </div>
              <div className={styles.pluginDetailHeaderLeftInfoSubWrapper}>
                <span>
                  Saved {dayjs(pluginDetail?.baseInfo?.updatedAt).fromNow()}
                </span>
                {pluginDetail?.formInfo?.status && (
                  <span className={styles.pluginDetailHeaderLeftInfoStatus}>
                    ({pluginDetail?.formInfo?.status})
                  </span>
                )}
              </div>
            </div>
          </div>

          {opType !== 'create' ? (
            <Tabs
              defaultActiveKey={curTab}
              activeKey={curTab}
              animated={{ inkBar: false }}
              tabBarGutter={12}
              items={PluginTabItems}
              rootClassName={styles.pluginDetailTabWrapper}
              onChange={setCurTab}
            />
          ) : (
            <Steps
              size="small"
              className={styles.pluginDetailStepsWrapper}
              current={PluginTabItems.findIndex(i => i.key === curTab)}
              items={PluginTabItems.map(i => ({ title: i.label }))}
              onChange={idx => setCurTab(PluginTabItems[idx].key)}
            />
          )}
          <i />
        </div>

        {loading ? (
          <Loading />
        ) : (
          <>
            <div
              className={cls({
                [styles.pluginDetailHide]: curTab !== 'basicInfo',
              })}
            >
              <PluginBasicInfo ref={basicInfoRef} />
            </div>

            {curTab === 'configurationForm' && configTreeData && (
              <ConfigurationForm
                key={'configurationForm'}
                wrapperClassName={styles.pluginDetailDynamicFormWrapper}
                renderWrapperClassName={
                  styles.pluginDetailDynamicFormRenderWrapper
                }
                setTreeRoot={setConfigTreeData}
                treeRoot={configTreeData}
              />
            )}

            {curTab === 'workflowForm' && workflowTreeData && (
              <WorkflowForm
                key={'workflowForm'}
                wrapperClassName={cls(styles.pluginDetailDynamicFormWrapper)}
                setTreeRoots={setWorkflowTreeData}
                treeRoots={workflowTreeData}
              />
            )}

            <Endpoint
              ref={endpointRef}
              configurationForm={configTreeData}
              forms={endpoints}
              wrapperClassName={cls({
                [styles.pluginDetailHide]: curTab !== 'endpointMapping',
              })}
            />

            <TestPlugin
              forms={
                (workflowTreeData?.map(form => ({
                  name: form.extends?.name ?? '',
                  endpoint: Reflect.get(finalEndpoints, form.formItemId),
                  structure: form,
                })) ?? []) as any
              }
              wrapperClassName={cls({
                [styles.pluginDetailHide]: curTab !== 'testPublish',
              })}
            />
          </>
        )}
      </div>
    </OperationContext.Provider>
  )
}

export default PluginDetail
