$error-color: rgba(255, 77, 79);

.input-area-file-card {
  &-wrapper {
    position: relative;
    display: flex;
    flex-flow: column;
    padding: 12px;
    width: 264px;
    height: 64px;
    box-sizing: border-box;
    border-radius: 8px;
    background-color: #f5f5f5;
    border: 1px solid transparent;

    &-error {
      border: 1px solid $error-color;
    }
  }

  &-content {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    column-gap: 16px;
  }

  &-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url('/assets/images/file2.svg');
    background-repeat: no-repeat;

    &-wrapper {
      display: flex;
      padding: 8px;
      background-color: white;
      border-radius: 8px;
    }
  }

  &-info {
    &-wrapper {
      display: inline-grid;
      flex: 1;
    }

    &-title {
      font-size: 14px;
      font-weight: 600;

      &-error {
        color: $error-color;
      }
    }

    &-type {
      font-size: 14px;
      font-weight: 400;

      &-error {
        color: $error-color;
      }
    }
  }

  &-delete {
    background-color: #d1d1d1;
    border: unset;

    &:hover {
      background-color: #c9c9c9 !important;
    }

    &-error {
      color: $error-color;

      &:hover {
        color: $error-color !important;
      }
    }
  }

  &-progress {
    position: absolute;

    width: 96%;
    bottom: -5px;
    left: 2%;
  }
}
