import { ReadOutlined } from '@ant-design/icons'
import ReactJson from '@microlink/react-json-view'
import { Flex, Modal, Skeleton, Space } from 'antd'
import {
  GenieCoreAppHandlersChatResponsesNodeInfoResponse,
  type GenieCoreAppHandlersSmartApiResponsesNodeInfoResponse,
} from 'api/data-contracts'
import { useGlobalStore } from 'stores/global'

export interface NodeDetailsModalProps {
  msgId: string
  nodeIndex: number
  open: boolean
  onCancel?: () => void
  loading: boolean

  nodeInfoDetails:
    | GenieCoreAppHandlersChatResponsesNodeInfoResponse[]
    | GenieCoreAppHandlersSmartApiResponsesNodeInfoResponse[]
}

const NodeDetailsModal: React.FC<NodeDetailsModalProps> = props => {
  const { theme } = useGlobalStore()
  return (
    <Modal
      title={
        <Space>
          <ReadOutlined />
          Node Information Details
        </Space>
      }
      open={props.open}
      width="80%"
      footer={null}
      onCancel={() => props.onCancel?.()}
    >
      <Flex style={{ padding: '12px 0', overflow: 'auto' }}>
        {props.loading ? (
          <Skeleton active />
        ) : (
          <ReactJson
            src={props.nodeInfoDetails?.[props.nodeIndex] ?? {}}
            name={false}
            displayDataTypes={false}
            style={{
              flexGrow: 1, // fixes bg half of screen only in dark mode
            }}
            theme={theme === 'dark' ? 'harmonic' : 'rjv-default'}
          />
        )}
      </Flex>
    </Modal>
  )
}

export default NodeDetailsModal
