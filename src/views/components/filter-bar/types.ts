import type { SelectProps } from 'antd'
import type { SortValue } from 'types/common'

/**
 * The value of the filter bar
 */
export interface FilterState {
  search: string
  sort?: SortValue | null
  filter?: Record<string, string[]> | null // e.g {orgnization_ids: [], category_ids: []}
}

/**
 * The options for the sort dropdown, key is used as the value of FilterState
 */
export interface SortOption {
  key: string // unique identifier, e.g name, category, uuid123
  label?: string // default to key.capFirst()
  ascLabel: string // e.g. A-Z, 1-9,
  descLabel: string // e.g. Z-A, 9-1
  hidden?: boolean // hide the item from dropdown
}

/**
 * The options for the filter dropdown, 'value' is used as the value of FilterState
 */
export type FilterOption = NonNullable<SelectProps['options']>[number]

/**
 * The options for the filter dropdowns
 * @example
 *   {
 *      key: 'category_ids',
 *      label: 'Category Ids',
 *      options: [{ label: 'Category 1', value: 1 }],
 *   }
 */
export interface FilterOptionsGroup {
  key: string
  label: string
  options: FilterOption[]
}
