import type { SelectProps } from 'antd'
import { uniq } from 'lodash-es'
import type { FilterOptionsGroup, FilterState } from '../types'
import { createInitialFilterValue } from '../utils'

interface UseFilterDropdownParams {
  filterOptions?: readonly FilterOptionsGroup[]
  value: FilterState
  onChange: (values: Partial<FilterState>) => Promise<URLSearchParams> | void
}

export function useFilterDropdown({
  filterOptions,
  value,
  onChange,
}: UseFilterDropdownParams) {
  const handleFilterClear = (optionGroup: FilterOptionsGroup) => {
    const newFilter = { ...value.filter }
    delete newFilter[optionGroup.key]

    onChange({
      ...value,
      filter: Object.keys(newFilter).length > 0 ? newFilter : null,
    })
  }

  const handleFilterDeselect = (
    optionGroup: FilterOptionsGroup,
    deselectValue: string,
  ) => {
    const updatedArray = (value.filter?.[optionGroup.key] ?? []).filter(
      v => v !== deselectValue,
    )

    const newValue = {
      ...value.filter,
      [optionGroup.key]: updatedArray,
    }

    // Remove properties with empty arrays
    const cleanedValue = Object.fromEntries(
      Object.entries(newValue).filter(([_, values]) => values.length > 0),
    )

    onChange({
      ...value,
      filter: cleanedValue,
    })
  }

  const handleFilterSelect = (
    optionGroup: FilterOptionsGroup,
    selectValue: string,
  ) => {
    const filterValues =
      value.filter ?? createInitialFilterValue(filterOptions ?? [])

    const updatedFilter = {
      ...filterValues,
      [optionGroup.key]: uniq([
        ...(filterValues?.[optionGroup.key] ?? []),
        selectValue,
      ]),
    }

    onChange({
      ...value,
      filter: updatedFilter,
    })
  }

  const getFilterOption: SelectProps['filterOption'] = (input, option) => {
    if (typeof option?.label !== 'string') {
      return false
    }
    return option.label.toLowerCase().includes(input.toLowerCase())
  }

  return {
    handleFilterClear,
    handleFilterDeselect,
    handleFilterSelect,
    getFilterOption,
  }
}
