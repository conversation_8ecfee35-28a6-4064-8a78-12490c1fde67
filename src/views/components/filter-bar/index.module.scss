.sortDropdown {
  :global {
    .ant-dropdown-menu {
      padding: 4px 0;

      .ant-dropdown-menu-item-group-list {
        padding-bottom: 4px;
      }
    }

    /* Target items within a group */
    .ant-dropdown-menu-item-group-list .ant-dropdown-menu-item {
      padding: 2px 12px;
      margin: 1px 0;
      line-height: 1.4;
      height: auto;
      min-height: 24px;
      font-size: 14px;
    }

    /* Add some spacing to the group title */
    .ant-dropdown-menu-item-group-title {
      font-weight: 500;
      line-height: 1.4;
      margin-bottom: 1px;
      font-size: 14px;
    }

    /* Reduce spacing between groups */
    .ant-dropdown-menu-item-group:not(:first-child) {
      border-block-start: var(--ant-line-width) solid var(--ant-color-split);
    }

    .ant-dropdown-menu-item-group:not(:first-child)
      .ant-dropdown-menu-item-group-title {
      margin-top: 4px;
    }
  }
}

.filterDropdown {
  .contentStyle {
    width: 300px;
    padding: 4px 0 8px;
    list-style-type: none;
    background-color: var(--ant-color-bg-elevated);
    background-clip: padding-box;
    border-radius: var(--ant-border-radius-lg);
    outline: none;
    box-shadow: var(--ant-box-shadow-secondary);
  }

  .item {
    padding: var(--ant-dropdown-padding-block)
      var(--ant-control-padding-horizontal);
    color: var(--ant-color-text-description);
    transition: all var(--ant-motion-duration-mid);
  }

  .itemTitle {
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 1px;
    color: #666;
    font-size: 14px;
  }
}

.colorPrimary {
  color: var(--ant-color-primary);
}
