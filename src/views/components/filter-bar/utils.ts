import type { SortValue } from 'types/common'

import type { MenuProps } from 'antd'
import { snakeCaseToWords } from 'utils/format'
import type { FilterOptionsGroup, FilterState, SortOption } from './types'

export function toSortMenuItems(
  option: SortOption,
): NonNullable<MenuProps['items']>[number] {
  if (option.hidden) return null
  return {
    key: option.key,
    type: 'group',
    label: option.label ?? snakeCaseToWords(option.key),
    children: [
      {
        key: `${option.key}|asc`,
        label: option.ascLabel,
      },
      {
        key: `${option.key}|desc`,
        label: option.descLabel,
      },
    ],
  }
}

export function toSortValue(
  selectedKeys: string[],
): SortValue | Record<string, never> {
  return selectedKeys.reduce<SortValue>((pre, cur) => {
    const [key, value] = cur.split('|')
    Reflect.set(pre, key, value)
    return pre
  }, {})
}

export const createInitialFilterValue = (
  optionsGroups: readonly FilterOptionsGroup[],
): Exclude<FilterState['filter'], undefined> => {
  return optionsGroups.reduce<Record<string, string[]>>((acc, curr) => {
    Reflect.set(acc, curr.key, [])
    return acc
  }, {})
}
