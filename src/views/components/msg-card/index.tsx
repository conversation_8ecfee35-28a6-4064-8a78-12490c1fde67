import cls from 'classnames'
import React, { memo } from 'react'

import styles from './index.scss'

// Should have a MsgType Here
type CardType = 'text'

export type MsgCardType = {
  // msg: MsgType
  type: CardType
  onClick?: (...args: any[]) => void
  children?: React.ReactNode | React.ReactNode[]
  wrapperClassName?: string

  [key: string]: any
}

const MsgCard = (props: MsgCardType) => {
  const { type, onClick, children, wrapperClassName } = props

  const handleMsgCardClick = () => {
    onClick?.(props)
  }

  const getCardContent = (cardType: CardType) => {
    if (cardType === 'text') {
      return <> {children} </>
    }

    return <>{children}</>
  }

  return (
    <div
      role="button"
      tabIndex={0}
      className={cls(styles.cardWrapper, wrapperClassName)}
      onClick={handleMsgCardClick}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          handleMsgCardClick()
        }
      }}
    >
      {getCardContent(type)}
    </div>
  )
}

export default memo(MsgCard)
