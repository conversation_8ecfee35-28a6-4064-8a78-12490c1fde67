import { Button, Flex, Typography } from 'antd'
import { useState } from 'react'
import { ObjectEditorModal } from './modal'

interface ObjectEditorProps {
  value?: any
  onChange?: (value: any) => void
}

export function ObjectEditor(props: Readonly<ObjectEditorProps>) {
  const [isEditing, setIsEditing] = useState(false)

  return (
    <Flex gap={24} justify="space-between" align="center">
      <Typography.Text ellipsis>{JSON.stringify(props.value)}</Typography.Text>
      <Button onClick={() => setIsEditing(true)} type="link">
        Edit
      </Button>
      <ObjectEditorModal
        value={props.value}
        open={isEditing}
        onCancel={() => setIsEditing(false)}
        onSave={value => {
          props.onChange?.(value)
          setIsEditing(false)
        }}
      />
    </Flex>
  )
}
