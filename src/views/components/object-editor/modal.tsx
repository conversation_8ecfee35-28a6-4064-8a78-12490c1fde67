import { Editor } from '@monaco-editor/react'
import { App, Flex, Modal } from 'antd'
import { useEffect, useState } from 'react'
import { isStringValidObject } from './utils'

interface ObjectEditorModalProps {
  value: any
  open: boolean
  onCancel: () => void
  onSave: (value: any) => void
}

export function ObjectEditorModal({
  value,
  open,
  onCancel,
  onSave,
}: Readonly<ObjectEditorModalProps>) {
  const [jsonString, setJsonString] = useState<string>(value)
  const { message } = App.useApp()

  useEffect(() => {
    setJsonString(JSON.stringify(value, null, 2))
  }, [open])

  function handleChange(value: string | undefined) {
    if (value) {
      setJsonString(value)
    }
  }

  function handleSave() {
    if (!isStringValidObject(jsonString, true)) {
      message.error('Invalid object')
      return false
    }
    onSave(JSON.parse(jsonString))
  }

  return (
    <Modal
      keyboard={false}
      title="Edit Object"
      open={open}
      destroyOnClose
      onCancel={onCancel}
      centered
      onOk={handleSave}
      okText="Save"
      width={500}
    >
      <Flex>
        <Editor
          height={'300px'}
          value={jsonString}
          onChange={handleChange}
          language="json"
          options={{
            minimap: { enabled: false },
            folding: true,
            scrollBeyondLastLine: false,
            fontSize: 12,
            lineHeight: 20,
            fontFamily: 'Fira Code, monospace',
            wordWrap: 'on',
          }}
        />
      </Flex>
    </Modal>
  )
}
