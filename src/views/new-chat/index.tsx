import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import cls from 'classnames'
import dayjs from 'dayjs'
import { observer } from 'mobx-react'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'

import { Conversation, WorkflowNode } from 'stores/models/public-chat'
import PublicChatStore from 'stores/public-chat'

import { safeJsonParse } from 'utils/common'
import CustomInputArea from 'views/components/custom-input-area'
import Message from '../components/message'
import ChatPageGuidance from './components/guidance'
import ChatPagNavbar from './components/navbar'
import ChatPageTitle from './components/title'

import { v1ChatPublishChannelList } from 'api/PublicApi'
import { useCreateHtmlScript } from 'hooks/use-create-html-script'
import { BaseChatExtraDataType, ChatQueryType } from './types'

import StaticFunction from 'components/antd-static-function'
import { assignIn } from 'lodash-es'
import { useChatInputProps } from './hooks/use-chat-input-props'
import styles from './index.scss'

export type ChatPageExtraDataType = BaseChatExtraDataType & {
  css: string
  favicon: string
  [key: string]: any
}

const ChatBubble = () => {
  const { uuid } = useParams<ChatQueryType>()
  const { notification } = StaticFunction

  const [loading, setLoading] = useState<boolean>(true)
  const [sending, setSending] = useState<boolean>(false)
  const started = useMemo(
    () => PublicChatStore.conversations.length > 1,
    [PublicChatStore.conversations.length],
  )

  const [startNodeData, setStartNodeData] = useState<WorkflowNode>()
  const [extraData, setExtraData] = useState<ChatPageExtraDataType>()
  const [userInput, setUserInput] = useState<string>('')
  const [isTCModalVisible, setIsTCModalVisible] = useState<boolean>(true)
  const conversionRef = useRef<HTMLDivElement>(null)
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  const documentRef = useRef<{ document_id: string; file_name: string }>()

  const isExceedLimited = useMemo(() => {
    if (
      extraData?.maxCharLength === undefined ||
      extraData?.maxCharLength === 0
    )
      return false

    const maxCharLength = Number(extraData.maxCharLength)
    return userInput.length >= maxCharLength
  }, [extraData?.maxCharLength, userInput])

  const fetchPageData = async () => {
    if (!uuid) {
      notification.error({
        message: 'Fetch Data Error',
        description: 'No uuid found',
      })
      return
    }
    setLoading(true)
    const { data } = await v1ChatPublishChannelList({ uuid })

    if (!data) {
      notification.error({
        message: 'Fetch Data Error',
        description: 'No data found',
      })
      return
    }

    const { title, nodes: nodesStr = '', extraData: extraDataStr = '' } = data
    document.title = title ?? ''
    const nodes = safeJsonParse<WorkflowNode[]>(nodesStr)
    const startNode = nodes?.find(node => node.type === 'Start')
    if (!startNode) {
      notification.error({
        message: 'Display Page Error',
        description: 'No start node found in the workflow',
      })
      return
    }
    setStartNodeData(startNode)

    const extraData = safeJsonParse<ChatPageExtraDataType>(extraDataStr)
    if (!extraData) {
      notification.error({
        message: 'Display Page Error',
        description: 'No extraData found in the channel',
      })
      return
    }

    setExtraData(extraData)

    PublicChatStore.setDefaultConversation([
      {
        id: '',
        sessionUUID: '',
        userID: '',
        organizationID: '',
        content: extraData.conversationOpener ?? '',
        role: 'assistant',
        tokens: 0,
        createdAt: dayjs().toISOString(),
        error: false,
        isLoading: false,
        currentNodeID: '',
        currentNodeLabel: '',
        startTime: '',
        endTime: '',
        like: false,
        unlike: false,
        document: {
          document_id: '',
          file_name: '',
        },
      },
    ])
    setLoading(false)
  }

  const handleSendMessage = useCallback(
    (param?: Partial<Conversation>) => {
      if (sending) return false
      if (userInput.trim() === '') {
        notification.error({
          message: 'Send Message Error',
          description: 'Please enter a prompt before sending',
        })
        return false
      }

      if (isExceedLimited) {
        notification.error({
          message: 'Send Message Error',
          description:
            'Message exceeds character limit. Please shorten your message',
        })
        return false
      }

      if (param?.document?.document_id) {
        documentRef.current = param?.document
      }

      const content = userInput
      setUserInput('')
      setSending(true)
      PublicChatStore.send(
        assignIn(param, { document: documentRef.current }, { content }),
        { uuid },
      )
        .then(() => {
          setSending(false)
        })
        .catch(error => {
          notification.error({
            message: 'Send Message Error',
            description: error,
          })
          setSending(false)
        })
      return true
    },
    [documentRef.current, userInput, isExceedLimited, sending],
  )

  const handleGuidanceCardClick = (msg: string) => {
    setUserInput(msg)
    textAreaRef.current?.focus()
  }

  // 1. auto-increment
  // 2. update callback
  const handleScrollToNewMessage = () => {
    conversionRef?.current?.scrollTo({
      behavior: 'smooth',
      top: conversionRef?.current?.scrollHeight,
    })
  }

  const chatInputProps = useChatInputProps({
    extraData,
    userInput,
    setUserInput,
    sending,
    startNodeData,
    textAreaRef,
  })

  useEffect(() => {
    fetchPageData()
  }, [])
  useEffect(handleScrollToNewMessage, [PublicChatStore.conversations.length])
  useCreateHtmlScript('style', extraData?.css)

  return (
    <Spin spinning={loading} tip={'Loading...'} className={styles.chatLoading}>
      <div className={cls('chat-page-wrapper', styles.chatPageWrapper)}>
        <div className={cls('chat-wrapper', styles.chatWrapper)}>
          {/* Bar */}
          <ChatPagNavbar
            open={!started}
            fqaBtnText={extraData?.faqButtonText}
            fqaContent={extraData?.userGuide}
            newChatButtonText={extraData?.newChatButtonText}
          />
          {/* Title */}
          <ChatPageTitle
            wrapperClassName={cls({ [styles.chatStartedConversion]: started })}
            title={extraData?.headerTitle}
            subTitle={extraData?.subheader}
          />
          {/* Guidance Group */}
          <ChatPageGuidance
            wrapperClassName={cls({ [styles.chatStartedConversion]: started })}
            dataList={extraData?.openingQuestions ?? []}
            onItemClick={handleGuidanceCardClick}
          />
          {/* Chat Box */}
          <div
            ref={conversionRef}
            className={cls(styles.chatBoxWrapper, 'chat-box-wrapper')}
          >
            {PublicChatStore.conversations?.map((msg, idx) => {
              let isShowFileCard = true
              if (idx > 2) {
                isShowFileCard = !(
                  PublicChatStore.conversations?.[idx - 2]?.document
                    ?.document_id === msg.document?.document_id
                )
              }

              return (
                <Message
                  key={`msg-${msg.id}-${msg.content}-${idx}`}
                  message={msg}
                  onUpdate={handleScrollToNewMessage}
                  feedback
                  showFileCard={isShowFileCard}
                  extraData={extraData}
                />
              )
            })}
          </div>
          {/* InputArea */}
          <div
            className={cls(
              'chat-input-area-wrapper',
              styles.chatInputAreaWrapper,
            )}
          >
            <CustomInputArea
              {...chatInputProps}
              wrapperClassName={cls({
                [styles.chatInputAreaExceedLimited]: isExceedLimited,
              })}
              onSend={handleSendMessage}
            />
          </div>
          {/* Footer */}
          <div
            className={cls('chat-footer', styles.chatFooter)}
            dangerouslySetInnerHTML={{
              __html: extraData?.disclaimerText ?? '',
            }}
          />
          {/* Modal */}
          <Modal
            className={cls('tc-modal-wrapper')}
            closable={false}
            maskClosable={false}
            open={!loading && isTCModalVisible}
            footer={
              <Button
                type="primary"
                onClick={() => setIsTCModalVisible(!isTCModalVisible)}
              >
                I Accept
              </Button>
            }
          >
            <div
              dangerouslySetInnerHTML={{
                __html: extraData?.termsAndConditions ?? '',
              }}
            />
          </Modal>
        </div>
      </div>
    </Spin>
  )
}

export default memo(observer(ChatBubble))
