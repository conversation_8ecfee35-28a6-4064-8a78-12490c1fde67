import { TextAreaProps } from 'antd/es/input'
import React, { RefObject, useMemo } from 'react'
import { WorkflowNode } from 'stores/models/public-chat'

interface UseChatInputPropsParams<T extends { [key: string]: any }> {
  extraData?: T
  userInput: string
  setUserInput: React.Dispatch<React.SetStateAction<string>>
  sending: boolean
  startNodeData?: WorkflowNode
  textAreaRef: RefObject<HTMLTextAreaElement>
}

export const useChatInputProps = <T extends { [key: string]: any }>({
  extraData,
  userInput,
  setUserInput,
  sending,
  startNodeData,
  textAreaRef,
}: UseChatInputPropsParams<T>) => {
  const inputProps = useMemo<TextAreaProps>(
    () => ({
      autoSize: { minRows: 1, maxRows: 5 },
      placeholder: extraData?.inputPlaceholder,
      maxLength: extraData?.maxCharLength ? extraData.maxCharLength : undefined,
      variant: 'borderless', // Always borderless for original look
      value: userInput,
    }),
    [extraData?.inputPlaceholder, extraData?.maxCharLength, userInput],
  )

  const uploadProps = useMemo(
    () => ({
      ...(extraData?.supportedFileTypes
        ? {
            accept: extraData?.supportedFileTypes.join(', '),
          }
        : {}),
      enableFileTypeCheck: extraData?.enableFileTypeCheck ?? false,
      maxCount: 1,
      open:
        startNodeData?.data?.data?.input.find(
          f => f.name === 'chat_to_document',
        )?.value ?? false,
      maxFileSize: startNodeData?.data?.data.input.find(
        f => f.name === 'file_size_limit',
      )?.value,
    }),
    [
      extraData?.supportedFileTypes,
      extraData?.enableFileTypeCheck,
      startNodeData,
    ],
  )

  return {
    inputProps,
    uploadProps,
    sending,
    onInputChange: setUserInput,
    inputRef: textAreaRef,
  }
}
