import { memo } from 'react'
import cls from 'classnames'

import styles from './index.scss'

type ChatTitleType = {
  wrapperClassName?: string
  title?: string
  subTitle?: string
}

const ChatTitle = (props: ChatTitleType) => {
  const { wrapperClassName, title, subTitle } = props
  return (
    <div
      className={cls(
        'chat-title-wrapper',
        wrapperClassName,
        styles.chatTitleWrapper,
      )}
    >
      <div className={cls('chat-title-header', styles.chatTitleHeader)}>
        {title}
      </div>

      <div className={cls('chat-title-sub', styles.chatTitleSub)}>
        {subTitle}
      </div>
    </div>
  )
}

export default memo(ChatTitle)
