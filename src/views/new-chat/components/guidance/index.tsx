import { memo, useState } from 'react'
import cls from 'classnames'

import styles from './index.scss'
import MsgCard from 'views/components/msg-card'

type ChatGuidanceType = {
  wrapperClassName?: string
  dataList: string[]
  onItemClick: (item: string) => void
}

const ChatGuidance = (props: ChatGuidanceType) => {
  const { wrapperClassName, dataList, onItemClick } = props

  const [curSelected, setCurSelected] = useState<Number>()

  const handleCardClick = (item: string, idx: number) => {
    onItemClick(item)
    setCurSelected(idx)
  }

  return (
    <div
      className={cls(wrapperClassName, {
        'chat-guidance-wrapper': true,
        [styles.chatGuidanceWrapper]: true,
      })}
    >
      {dataList?.map((item, idx) => {
        return (
          <MsgCard
            type="text"
            onClick={() => handleCardClick(item, idx)}
            key={item}
            wrapperClassName={cls({
              'chat-guidance-card-wrapper': true,
              'chat-guidance-card-wrapper-selected': idx === curSelected,
              [styles.chatGuidanceCardWrapper]: true,
            })}
          >
            <i
              className={cls(
                'chat-guidance-card-text-icon',
                styles.chatGuidanceCardTextIcon,
              )}
            />

            <span
              className={cls(
                'chat-guidance-card-text',
                styles.chatGuidanceCardText,
              )}
            >
              {item}
            </span>
          </MsgCard>
        )
      })}
    </div>
  )
}

export default memo(ChatGuidance)
