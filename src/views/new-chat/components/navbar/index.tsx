import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Modal, Tooltip } from 'antd'
import { v1ChatConversationsDownloadList } from 'api/PublicApi'
import cls from 'classnames'
import { memo, useState } from 'react'
import PublicChatStore from 'stores/public-chat'

import { currentTimezone } from 'utils/filter'
import styles from './index.scss'

type NavbarType = {
  open: boolean
  newChatButtonText?: string

  fqaBtnText?: string
  fqaContent?: string
}

const ChatPagNavbar = (props: NavbarType) => {
  const { open, newChatButtonText, fqaBtnText, fqaContent } = props
  const [isFqaModalVisible, setIsFqaModalVisible] = useState(false)

  const handleDownloadLog = async () => {
    const logResp = await v1ChatConversationsDownloadList(
      {
        session_uuid: PublicChatStore.sessionUUID,
        latest_no_conversations: 200,
        timezone_offset: currentTimezone(),
      },
      { responseType: 'blob' },
    )

    if (!logResp) return
    const blob = new Blob([logResp.data], {
      type: 'application/pdf',
    })
    const fileUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = fileUrl
    link.setAttribute('download', 'conversation-history.pdf')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className={cls(styles.navbarWrapper, 'chat-bar-wrapper')}>
      {!open && (
        <Tooltip
          placement="bottom"
          trigger={'hover'}
          title={'Conversation Log'}
          overlayClassName={cls(
            styles.navbarIconTooltip,
            'chat-bar-help-icon-tooltip',
          )}
        >
          <DownloadOutlined
            className={cls(
              styles.navbarIcon,
              styles.navbarIconLog,
              'chat-bar-log-icon',
            )}
            onClick={handleDownloadLog}
          />
        </Tooltip>
      )}
      <Tooltip
        placement="bottom"
        trigger={'hover'}
        title={fqaBtnText ?? ''}
        overlayClassName={cls(
          styles.navbarIconTooltip,
          'chat-bar-help-icon-tooltip',
        )}
      >
        <QuestionCircleOutlined
          className={cls(
            styles.navbarIcon,
            styles.navbarIconFqa,
            'chat-bar-help-icon',
          )}
          onClick={() => setIsFqaModalVisible(true)}
        />
      </Tooltip>
      <Button
        type="default"
        onClick={() => window.location.reload()}
        className={cls({
          'chat-bar-new-chat': true,
          [styles.navbarNewChat]: true,
          [styles.navbarNewChatHide]: open,
        })}
      >
        <i
          className={cls({
            'chat-bar-new-chat-icon': true,
            [styles.navbarNewChatIcon]: true,
          })}
        />
        <span className={cls(styles.navbarNewChatText)}>
          {newChatButtonText}
        </span>
      </Button>
      <Modal
        closable={false}
        maskClosable={false}
        open={isFqaModalVisible}
        footer={
          <Button type="primary" onClick={() => setIsFqaModalVisible(false)}>
            Got it
          </Button>
        }
      >
        <div dangerouslySetInnerHTML={{ __html: fqaContent ?? '' }} />
      </Modal>
    </div>
  )
}

export default memo(ChatPagNavbar)
