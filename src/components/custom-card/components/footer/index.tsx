import { EllipsisOutlined } from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { Button, Dropdown } from 'antd'
import classNames from 'classnames'
import { MouseEvent, ReactNode, type PropsWithChildren } from 'react'
import styles from './index.module.scss'

interface BaseFooterProps {
  tags?: ReactNode | ReactNode[] // tags are displayed at left side of the footer
  actions?: ReactNode | ReactNode[] // actions are displayed when card is hovered
  className?: string
}

interface FooterWithDropdown extends BaseFooterProps {
  dropdownItems: Required<MenuProps>['items']
  handleMenuClick: MenuProps['onClick']
}

interface FooterWithoutDropdown extends BaseFooterProps {
  dropdownItems?: never
  handleMenuClick?: never
}

type FooterProps = FooterWithDropdown | FooterWithoutDropdown

export function Footer({
  tags,
  actions,
  dropdownItems,
  handleMenuClick,
  className,
  children,
}: PropsWithChildren<FooterProps>) {
  const handleClick = (e: MouseEvent) => {
    e.stopPropagation()
  }

  return (
    <section
      className={classNames(styles.footer, className)}
      onClick={handleClick}
      role="button"
    >
      {tags ? <div className={styles.tags}>{tags}</div> : null}
      <nav className={styles.actions}>
        {actions ? (
          <div
            className={classNames(styles.actionButtons, 'actionButtonsVisible')}
          >
            {actions}
          </div>
        ) : null}
        {dropdownItems ? (
          <Dropdown
            menu={{
              items: dropdownItems,
              onClick: handleMenuClick,
            }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button
              type="text"
              icon={<EllipsisOutlined />}
              className={styles.ellipsisButton}
            />
          </Dropdown>
        ) : null}
      </nav>
      {children}
    </section>
  )
}

export default Footer
