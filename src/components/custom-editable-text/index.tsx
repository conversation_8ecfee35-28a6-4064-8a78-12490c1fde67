import { ExclamationCircleOutlined } from '@ant-design/icons'
import { Input, InputProps, Typography } from 'antd'
import { TextAreaProps } from 'antd/es/input'
import cls from 'classnames'
import React, { useEffect, useRef, useState } from 'react'
import styles from './index.scss'

const { Text } = Typography
const { TextArea } = Input

interface EditableTextProps {
  value: string
  wrapperClassName?: string
  textClassName?: string
  type?: 'input' | 'textarea'
  inputProps?: InputProps
  textareaProps?: TextAreaProps
  disabled?: boolean
  placeholder?: string

  onChange?: (value: string) => void
  validator?: (value: string) => { isValid: boolean; errorMessage?: string }
}

const EditableText = (props: EditableTextProps) => {
  const {
    value,
    wrapperClassName,
    textClassName,
    type,
    inputProps,
    textareaProps,
    disabled,
    placeholder,

    onChange,
    validator,
  } = props
  const [editing, setEditing] = useState(false)
  const [text, setText] = useState(value)
  const [errorMsg, setErrorMsg] = useState<string | undefined>(undefined)
  const inputRef = useRef<any>(null)

  useEffect(() => {
    setText(value)
  }, [value])

  useEffect(() => {
    if (editing && inputRef.current) {
      inputRef.current.focus()
    }
  }, [editing])

  const validate = (value: string): boolean => {
    if (validator) {
      const result = validator(value)
      if (!result.isValid) {
        setErrorMsg(result.errorMessage ?? 'Invalid input')
        return false
      }
    }

    setErrorMsg(undefined)
    return true
  }

  const handleClick = () => {
    if (!disabled) {
      setEditing(true)
    }
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const newValue = e.target.value
    setText(newValue)
    validate(newValue)
  }

  const handleBlur = () => {
    if (validate(text)) {
      setEditing(false)
      if (onChange && text !== value) {
        onChange(text)
      }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && type !== 'textarea') {
      e.preventDefault()
      handleBlur()
    } else if (e.key === 'Escape') {
      setText(value)
      setErrorMsg(undefined)
      setEditing(false)
    }
  }

  const inputDefaultProps = {
    ref: inputRef,
    value: text,
    onChange: handleChange,
    onBlur: handleBlur,
    onKeyDown: handleKeyDown,
    placeholder,
  }

  return (
    <div className={cls(styles.CustomEditableTextWrapper, wrapperClassName)}>
      {editing ? (
        <div className={styles.inputWrapper}>
          {type === 'textarea' ? (
            <TextArea
              status={errorMsg ? 'error' : undefined}
              {...inputDefaultProps}
              {...textareaProps}
            />
          ) : (
            <Input
              status={errorMsg ? 'error' : undefined}
              {...inputDefaultProps}
              {...inputProps}
              className={cls({
                [styles.customEditableTextErrorMessage]: !!errorMsg,
              })}
            />
          )}
          {errorMsg && (
            <div className={styles.customEditableTextErrorMessage}>
              <ExclamationCircleOutlined /> {errorMsg}
            </div>
          )}
        </div>
      ) : (
        <div className={styles.customEditableTextTextWrapper}>
          <div onClick={handleClick} className={textClassName} role="button">
            {value || placeholder}
          </div>
        </div>
      )}
    </div>
  )
}

export default EditableText
