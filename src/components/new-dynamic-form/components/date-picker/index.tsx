import { memo, useMemo } from 'react'
import { DatePicker, DatePickerProps } from 'antd'
import ComponentWrapper from '../lib/component-wrapper'
import { omitBy } from 'lodash-es'
import { getCommonDefaultValue } from '../constants'
import ItemDatePickerConfig from './config'
import { isDayjs } from 'dayjs'

type ItemDatePickerProps = {
  datePickerConfig?: DatePickerProps & {
    rangePicker: boolean
    [key: string]: any
  }
} & Record<string, any>

const ItemDatePicker = (props: ItemDatePickerProps) => {
  const { datePickerConfig } = props

  const filterProps = useMemo(
    () => omitBy(props, (_, k) => k !== 'onChange' && /[A-Z]/.test(k)),
    [props],
  )

  return (
    <DatePicker
      {...filterProps}
      {...(datePickerConfig as DatePickerProps)}
      value={isDayjs(props.value) ? props.value : undefined}
    />
  )
}

export default ComponentWrapper(memo(ItemDatePicker))
export const getDefaultValue = () => {
  const defaultValDataType = ItemDatePickerConfig.find(
    c => c.sectionLabel === 'generalConfig',
  )?.items.find(i => i.itemName === 'dataType')?.defaultValue
  return getCommonDefaultValue('datePicker', {
    props: { generalConfig: { dataType: defaultValDataType } },
  })
}
