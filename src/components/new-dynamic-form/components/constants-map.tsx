import { ComponentType } from 'react'
// Render And AssociateAttribute Function
import ItemCheckbox from './checkbox'
import ItemCode from './code'
import ItemCondition from './condition'
import ItemDatePicker from './date-picker'
import ItemInput from './input'
import ItemInputNumber, {
  associateAttribute as InputNumberAssociateAttribute,
} from './input-number'
import ItemRadio from './radio'
import ItemSelect from './select'
import ItemSlider from './slider'
import ItemSwitch from './switch'
import ItemTextArea from './text-area'
import ItemTimePicker from './time-picker'
import ItemUpload from './upload'
import ItemValueMaker from './value-maker'
import ItemValuePicker from './value-picker'

// Config
import ItemCheckboxConfig from './checkbox/config'
import ItemCodeConfig from './code/config'
import ItemConditionConfig from './condition/config'
import ItemDatePickerConfig from './date-picker/config'
import ItemInputNumberConfig from './input-number/config'
import ItemInputConfig from './input/config'
import ItemRadioConfig from './radio/config'
import ItemSelectConfig from './select/config'
import ItemSliderConfig from './slider/config'
import ItemSwitchConfig from './switch/config'
import ItemTextAreaConfig from './text-area/config'
import ItemTimePickerConfig from './time-picker/config'
import ItemUploadConfig from './upload/config'
import ItemValueMakerConfig from './value-maker/config'
import ItemValuePickerConfig from './value-picker/config'

import { ConfigSectionType, FormItemType, FormSelectorType } from '../types'
import ComponentWrapper, { WithDraggingProps } from './lib/component-wrapper'

export const FormItemRenderMap: Partial<
  Record<
    FormSelectorType,
    ComponentType<WithDraggingProps<FormItemType> & Record<string, any>> | null
  >
> = {
  root: null,
  input: ItemInput,
  inputNumber: ItemInputNumber,
  switch: ItemSwitch,
  slider: ItemSlider,
  checkbox: ItemCheckbox,
  radio: ItemRadio,
  select: ItemSelect,
  timePicker: ItemTimePicker,
  datePicker: ItemDatePicker,
  upload: ItemUpload,
  code: ItemCode,
  valuePicker: ItemValuePicker,
  valueMaker: ItemValueMaker,
  textArea: ItemTextArea,
  condition: ComponentWrapper(ItemCondition),
}

export const FormItemConfigMap: Partial<
  Record<FormSelectorType, ConfigSectionType[]>
> = {
  root: [],
  input: ItemInputConfig,
  inputNumber: ItemInputNumberConfig,
  switch: ItemSwitchConfig,
  slider: ItemSliderConfig,
  checkbox: ItemCheckboxConfig,
  radio: ItemRadioConfig,
  select: ItemSelectConfig,
  timePicker: ItemTimePickerConfig,
  datePicker: ItemDatePickerConfig,
  upload: ItemUploadConfig,
  code: ItemCodeConfig,
  valuePicker: ItemValuePickerConfig,
  valueMaker: ItemValueMakerConfig,
  textArea: ItemTextAreaConfig,
  condition: ItemConditionConfig,
}

export const formItemRegister = (
  key: FormSelectorType,
  info: Partial<{
    Render: ComponentType<
      WithDraggingProps<FormItemType> & Record<string, any>
    > | null
    config: ConfigSectionType[]
    associateAttribute: (
      item: FormItemType,
      changeValues?: FormItemType['props'],
    ) => FormItemType | void
  }>,
) => {
  const { Render, config, associateAttribute } = info
  if (Render) Reflect.set(FormItemRenderMap, key, Render)
  if (config) Reflect.set(FormItemConfigMap, key, config)
  if (associateAttribute)
    Reflect.set(
      componentAssociateAttributeChangeFunctionMap,
      key,
      associateAttribute,
    )
}

export const componentAssociateAttributeChangeFunctionMap: Partial<
  Record<
    FormSelectorType,
    (
      item: FormItemType,
      changeValues?: FormItemType['props'],
    ) => FormItemType | void
  >
> = {
  inputNumber: InputNumberAssociateAttribute,
  // list: ListAssociateAttribute,
  // valuePicker: ValuePickerAssociateAttribute,
}
