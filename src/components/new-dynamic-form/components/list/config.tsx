import {
  ConfigSectionType,
  FormItemType,
} from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { ALL_DATA_TYPE, isArrayType } from 'constants/common'
import { cloneDeep, get, set } from 'lodash-es'

const ItemListConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map(i => ({
            label: i,
            value: i,
          })),
          defaultValue: 'Array<Object>',
          disabled: true,
        },
      ],
    },
  ]),
  {
    sectionLabel: 'listConfig',
    items: [
      {
        itemLabel: 'Max',
        itemName: 'max',
        type: 'inputNumber',
        defaultValue: 0,
      },
    ],
  },
]

export const associateAttribute = (item: FormItemType) => {
  const newItem = cloneDeep(item)
  const { children } = newItem

  if (children?.length === 1) {
    const childrenType = get(children, '0.props.generalConfig.dataType')
    if (!isArrayType(childrenType)) {
      set(newItem, 'props.generalConfig.dataType', `Array<${childrenType}>`)
      return newItem
    }
  }

  set(newItem, 'props.generalConfig.dataType', 'Array<Object>')
  return newItem
}

export default ItemListConfig
