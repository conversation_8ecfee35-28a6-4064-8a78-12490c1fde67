import { ConfigSectionType } from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { PluginPluginDataType } from 'api/data-contracts'
import { ALL_DATA_TYPE } from 'constants/common'

const ItemSelectConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map(i => ({
            label: i,
            value: i,
          })),
          defaultValue: PluginPluginDataType.String,
          disabled: true,
        },
      ],
    },
  ]),
  {
    sectionLabel: 'selectConfig',
    items: [
      { itemLabel: 'AllowClear', itemName: 'allowClear', type: 'switch' },
      { itemLabel: 'Disabled', itemName: 'disabled', type: 'switch' },
      {
        itemLabel: 'Mode',
        itemName: 'mode',
        type: 'select',
        options: [
          { label: 'Multiple', value: 'multiple' },
          { label: 'Tags', value: 'tags' },
          { label: 'Single', value: 'single' },
        ],
      },
      { itemLabel: 'Placeholder', itemName: 'placeholder', type: 'input' },
      { itemLabel: 'DefaultValue', itemName: 'defaultValue', type: 'input' },
      {
        itemLabel: 'Options',
        itemName: 'options',
        type: 'compactInput',
        defaultValue: [
          [
            { label: 'Label', value: '' },
            { label: 'Value', value: '' },
          ],
        ],
        minCount: 1,
      },
    ],
  },
]

export default ItemSelectConfig
