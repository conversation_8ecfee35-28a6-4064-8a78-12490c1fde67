import {
  MinusCircleOutlined,
  MinusOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { Button, Form, Input, Select, Space, TreeSelect } from 'antd'
import { PluginPluginDataType } from 'api/data-contracts'
import { useFormContext } from 'components/new-dynamic-form/context'
import {
  convertFormItemsToTreeData,
  getDataFromTreeData,
  handleFilterTypeTreeData,
} from 'components/new-dynamic-form/util'
import { nanoid } from 'nanoid'
import React, { memo, useMemo } from 'react'
import { LogicTypeEnum, OperatorTypeEnum } from './constants'

import { getCommonDefaultValue } from '../constants'
import ItemConditionConfig from './config'

import styles from './index.scss'

const operatorOptions = [
  { value: OperatorTypeEnum.EQUAL, label: '==' },
  { value: OperatorTypeEnum.NOT_EQUAL, label: '!=' },
  { value: OperatorTypeEnum.GREATER_THAN, label: '>' },
  { value: OperatorTypeEnum.GREATER_THAN_OR_EQUAL, label: '>=' },
  { value: OperatorTypeEnum.LESS_THAN, label: '<' },
  { value: OperatorTypeEnum.LESS_THAN_OR_EQUAL, label: '<=' },
  { value: OperatorTypeEnum.IN, label: 'in' },
  { value: OperatorTypeEnum.NOT_IN, label: 'not_in' },
  { value: OperatorTypeEnum.CONTAINS, label: 'contains' },
  { value: OperatorTypeEnum.MATCHES, label: 'matches' },
]

// extracted component for target value input to reduce nesting
type TargetValueInputProps = {
  fieldName: number
  pathList: string[]
  groupName: number
  curTargetKeyTreeData: any
}

const TargetValueInput = memo(
  ({
    fieldName,
    pathList,
    groupName,
    curTargetKeyTreeData,
  }: TargetValueInputProps) => {
    return (
      <Form.Item noStyle shouldUpdate>
        {form => {
          const sourceKey = form.getFieldValue([
            ...pathList,
            groupName,
            'items',
            fieldName,
            'sourceKey',
          ])

          const sourceKeyDataType =
            getDataFromTreeData(curTargetKeyTreeData, sourceKey)?.dataType ??
            PluginPluginDataType.String

          if (!curTargetKeyTreeData) return null

          return (
            <Space.Compact block>
              <Form.Item
                name={[fieldName, 'target', 'type']}
                noStyle
                initialValue={'input'}
              >
                <Select
                  key={'targetType'}
                  popupMatchSelectWidth={false}
                  style={{ width: 'auto' }}
                  options={[
                    { label: 'Input', value: 'input' },
                    { label: 'Reference', value: 'reference' },
                  ]}
                />
              </Form.Item>

              <Form.Item noStyle shouldUpdate>
                {form => {
                  const targetType = form.getFieldValue([
                    ...pathList,
                    groupName,
                    'items',
                    fieldName,
                    'target',
                    'type',
                  ])

                  return (
                    <Form.Item name={[fieldName, 'target', 'value']} noStyle>
                      {targetType === 'input' ? (
                        <Input />
                      ) : (
                        <TreeSelect
                          placeholder="Select"
                          treeData={handleFilterTypeTreeData(
                            curTargetKeyTreeData as any,
                            sourceKeyDataType,
                          )}
                          popupMatchSelectWidth={false}
                        />
                      )}
                    </Form.Item>
                  )
                }}
              </Form.Item>
            </Space.Compact>
          )
        }}
      </Form.Item>
    )
  },
)

// extracted component for individual condition items to reduce nesting
type ConditionItemProps = {
  field: any
  groupKey: string | number
  pathList: string[]
  groupName: number
  curTargetKeyTreeData: any
  onRemove: (fieldName: number) => void
}

const ConditionItem = memo(
  ({
    field,
    groupKey,
    pathList,
    groupName,
    curTargetKeyTreeData,
    onRemove,
  }: ConditionItemProps) => {
    return (
      <div
        key={`${groupKey}-${field.key}`}
        className={styles.conditionGroupItem}
      >
        <Form.Item name={[field.name, 'sourceKey']} noStyle>
          <TreeSelect
            placeholder="Variable"
            treeData={curTargetKeyTreeData}
            popupMatchSelectWidth={false}
          />
        </Form.Item>

        <Form.Item name={[field.name, 'operator']} noStyle>
          <Select
            placeholder="Operator"
            options={operatorOptions.map(i => ({
              label: i.label,
              value: i.value,
            }))}
          />
        </Form.Item>

        <TargetValueInput
          fieldName={field.name}
          pathList={pathList}
          groupName={groupName}
          curTargetKeyTreeData={curTargetKeyTreeData}
        />

        <Button
          type="link"
          onClick={() => onRemove(field.name)}
          icon={<MinusCircleOutlined />}
        />
      </div>
    )
  },
)

// extracted component for logic type selector to reduce nesting
type LogicTypeSelectorProps = {
  groupName: number
  fieldsLength: number
}

const LogicTypeSelector = memo(
  ({ groupName, fieldsLength }: LogicTypeSelectorProps) => {
    if (fieldsLength <= 1) return null

    return (
      <Form.Item
        name={[groupName, 'logicType']}
        initialValue={LogicTypeEnum.and}
        className={styles.conditionLogicType}
      >
        <Select
          variant="borderless"
          options={Object.keys(LogicTypeEnum).map(i => ({
            label: i,
            value: i,
          }))}
        />
      </Form.Item>
    )
  },
)

// extracted component for condition groups to reduce nesting
type ConditionGroupProps = {
  group: any
  pathList: string[]
  curTargetKeyTreeData: any
  onRemove: (groupName: number) => void
  groupsLength: number
  min: number
}

const ConditionGroup = memo(
  ({
    group,
    pathList,
    curTargetKeyTreeData,
    onRemove,
    groupsLength,
    min,
  }: ConditionGroupProps) => {
    return (
      <div key={group.key} className={styles.conditionGroupWrapper}>
        <Form.Item name={[group.name, 'id']} hidden>
          {/* Antd Warning: Must have an element here */}
          <span></span>
        </Form.Item>

        <div className={styles.conditionGroupHeader}>
          <Form.Item
            name={[group.name, 'groupName']}
            noStyle
            className={styles.conditionGroupName}
          >
            <Input />
          </Form.Item>

          <Button
            type="link"
            icon={<MinusOutlined />}
            onClick={() => onRemove(group.name)}
            disabled={groupsLength === min}
          />
        </div>

        <span>IF</span>

        <Form.List name={[group.name, 'items']}>
          {(fields, { add: fieldAdd, remove: fieldRemove }) => (
            <React.Fragment>
              <div className={styles.conditionGroupItemWrapper}>
                <div className={styles.conditionGroupItemBracket}>
                  <LogicTypeSelector
                    groupName={group.name}
                    fieldsLength={fields.length}
                  />
                </div>

                <div className={styles.conditionGroupItemContent}>
                  {fields.map(field => (
                    <ConditionItem
                      key={`${group.key}-${field.key}`}
                      field={field}
                      groupKey={group.key}
                      pathList={pathList}
                      groupName={group.name}
                      curTargetKeyTreeData={curTargetKeyTreeData}
                      onRemove={fieldRemove}
                    />
                  ))}
                </div>
              </div>

              <Button
                type="link"
                icon={<PlusCircleOutlined />}
                onClick={() => fieldAdd()}
              >
                Add Condition
              </Button>
            </React.Fragment>
          )}
        </Form.List>
      </div>
    )
  },
)

export type ConditionItem = {
  sourceKey: string
  operator: OperatorTypeEnum
  target: {
    type: 'input' | 'reference'
    value: any
  }
}

export type ConditionGroup = Array<{
  id: string
  logicType: LogicTypeEnum
  groupName: string
  items: ConditionItem[]
}>

type CustomConditionProps = {
  // value: ConditionGroup[]
  // onChange?: (value: any) => void
  pathList: string[]

  conditionConfig?: {
    hasElse?: boolean
    max?: number
    min?: number
  }
}

const CustomCondition = (props: CustomConditionProps) => {
  const { pathList, conditionConfig } = props
  const { hasElse = false, max, min = 0 } = conditionConfig ?? {}
  const { root, referenceTreeData } = useFormContext() ?? {}
  const curTargetKeyTreeData = useMemo(
    () => referenceTreeData ?? convertFormItemsToTreeData(root?.children ?? []),
    [root],
  )

  return (
    <Form.List name={pathList}>
      {(groups, { add, remove }) => (
        <div className={styles.conditionItemWrapper}>
          {groups.map(group => (
            <ConditionGroup
              key={group.key}
              group={group}
              pathList={pathList}
              curTargetKeyTreeData={curTargetKeyTreeData}
              onRemove={remove}
              groupsLength={groups.length}
              min={min}
            />
          ))}

          {groups.length && hasElse ? (
            <div className={styles.conditionGroupWrapper}>
              <span className={styles.conditionGroupName}>ELSE</span>
            </div>
          ) : null}

          <Button
            color="primary"
            type="dashed"
            block
            disabled={max ? groups.length >= max : false}
            icon={<PlusOutlined />}
            onClick={() =>
              add({
                id: nanoid(4),
                logicType: LogicTypeEnum.and,
                groupName: `Condition ${groups.length + 1}`,
                items: [],
              })
            }
          >
            Add
          </Button>
        </div>
      )}
    </Form.List>
  )
}

export default memo(CustomCondition)
const getDefaultValue = () => {
  const defaultValDataType = ItemConditionConfig.find(
    c => c.sectionLabel === 'generalConfig',
  )?.items.find(i => i.itemName === 'dataType')?.defaultValue
  return getCommonDefaultValue('condition', {
    props: { generalConfig: { dataType: defaultValDataType } },
  })
}

export { getDefaultValue }
