import { ConfigSectionType } from 'components/new-dynamic-form/types'
import { getCommonConfig } from '../constants'
import { ALL_DATA_TYPE } from 'constants/common'
import { PluginPluginDataType } from 'api/data-contracts'

const ItemSectionConfig: ConfigSectionType[] = [
  ...getCommonConfig([
    {
      sectionLabel: 'generalConfig',
      items: [
        {
          itemLabel: 'DataType',
          itemName: 'dataType',
          type: 'select',
          options: Object.values(ALL_DATA_TYPE).map(i => ({
            label: i,
            value: i,
          })),
          defaultValue: PluginPluginDataType.Object,
          disabled: true,
        },
      ],
    },
  ]),
]

export default ItemSectionConfig
