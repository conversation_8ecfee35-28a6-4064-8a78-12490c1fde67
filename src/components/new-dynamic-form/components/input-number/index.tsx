import { memo, useMemo } from 'react'
import { InputNumber, InputNumberProps } from 'antd'
import ComponentWrapper from '../lib/component-wrapper'
import { omitBy } from 'lodash-es'
import { getCommonDefaultValue } from '../constants'
import ItemInputNumberConfig, { associateAttribute } from './config'

import styles from './index.scss'

type ItemInputNumberProps = { inputNumberConfig?: InputNumberProps } & Record<
  string,
  any
>

const ItemInputNumber = (props: ItemInputNumberProps) => {
  const { inputNumberConfig } = props

  const filterProps = useMemo(
    () =>
      omitBy(
        props,
        (_, k) => k !== 'onChange' && (k === 'children' || /[A-Z]/.test(k)),
      ),
    [props],
  )

  return (
    <InputNumber
      {...filterProps}
      {...inputNumberConfig}
      className={styles.inputNumber}
    />
  )
}

export default ComponentWrapper(memo(ItemInputNumber))
const getDefaultValue = () => {
  const defaultValDataType = ItemInputNumberConfig.find(
    c => c.sectionLabel === 'generalConfig',
  )?.items.find(i => i.itemName === 'dataType')?.defaultValue
  return getCommonDefaultValue('inputNumber', {
    props: { generalConfig: { dataType: defaultValDataType } },
  })
}
export { getDefaultValue, associateAttribute }
