import {
  attachClosestEdge,
  extractClosestEdge,
} from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge'
import { getReorderDestinationIndex } from '@atlaskit/pragmatic-drag-and-drop-hitbox/util/get-reorder-destination-index'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import {
  DragLocationHistory,
  DropTargetRecord,
  ElementDragPayload,
} from '@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types'
import {
  dropTargetForElements,
  monitorForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { reorder } from '@atlaskit/pragmatic-drag-and-drop/reorder'
import { cloneDeep } from 'lodash-es'
import { useEffect } from 'react'

import { defaultValueMap } from './constants'
import { FormItemType, FormSelectorType } from './types'
import { getItemByPath, updateItemListByPath } from './util'

const ContainerList = ['list', 'section']

const handleAddItem = (
  source: { itemType: FormSelectorType },
  target: FormItemType,
  targetParent: FormItemType | undefined,
  treeRoot: FormItemType,
  setTreeRoot: (node: FormItemType) => void,
) => {
  const sourceType = source.itemType
  const getDefaultValue = Reflect.get(defaultValueMap, sourceType)
  if (!getDefaultValue) return

  const newItem = getDefaultValue()
  const newRoot = cloneDeep(treeRoot)

  if (target.type === 'root') {
    newRoot.children.push({
      ...newItem,
      path: [...newRoot.path, newItem.formItemId],
    })
    setTreeRoot(newRoot)
  } else if (ContainerList.includes(target.type)) {
    const updateRoot = updateItemListByPath(treeRoot, target.path, [
      ...target.children,
      { ...newItem, path: [...target.path, newItem.formItemId] },
    ])
    if (updateRoot) setTreeRoot(updateRoot)
  } else if (targetParent) {
    const targetIdx = targetParent.children.findIndex(
      i => i.formItemId === target.formItemId,
    )
    const destinationIndex = getReorderDestinationIndex({
      startIndex: targetParent.children.length,
      indexOfTarget: targetIdx,
      closestEdgeOfTarget: extractClosestEdge(target),
      axis: 'vertical',
    })

    const newItemList = cloneDeep(targetParent.children)
    newItemList.splice(destinationIndex, 0, {
      ...newItem,
      path: [...targetParent.path, newItem.formItemId],
    })

    const updateRoot = updateItemListByPath(
      treeRoot,
      targetParent.path,
      newItemList,
    )
    if (updateRoot) setTreeRoot(updateRoot)
  }
}

const handleDropToRoot = (
  source: FormItemType,
  target: FormItemType,
  treeRoot: FormItemType,
  setTreeRoot: (node: FormItemType) => void,
) => {
  const newRoot = cloneDeep(treeRoot)

  if (source.path.length === 1) {
    const newItemList = updateItemListByPath(
      newRoot,
      newRoot.path,
      reorder({
        list: newRoot.children,
        startIndex: newRoot.children.findIndex(
          i => i.formItemId === source.formItemId,
        ),
        finishIndex: newRoot.children.findIndex(
          i => i.formItemId === target.formItemId,
        ),
      }),
    )
    setTreeRoot(newItemList)
  } else {
    const sourceParent = getItemByPath(newRoot, source.path.slice(0, -1))
    if (!sourceParent) return

    const delUpdateRoot = updateItemListByPath(
      newRoot,
      sourceParent.path,
      sourceParent.children.filter(i => i.formItemId !== source.formItemId),
    )
    const newItemList = updateItemListByPath(
      delUpdateRoot,
      delUpdateRoot.path,
      [
        ...delUpdateRoot.children,
        {
          ...source,
          path: [delUpdateRoot.formItemId, source.formItemId],
        },
      ],
    )
    setTreeRoot(newItemList)
  }
}

const handleSameContainerReorder = (
  source: FormItemType,
  target: FormItemType,
  targetParent: FormItemType,
  treeRoot: FormItemType,
  setTreeRoot: (node: FormItemType) => void,
) => {
  const sourceParent = getItemByPath(treeRoot, source.path.slice(0, -1))
  if (!sourceParent) return

  const sourceIdx = sourceParent.children.findIndex(
    i => i.formItemId === source.formItemId,
  )
  const destinationIndex = getReorderDestinationIndex({
    startIndex: sourceIdx,
    indexOfTarget: targetParent.children.findIndex(
      i => i.formItemId === target.formItemId,
    ),
    closestEdgeOfTarget: extractClosestEdge(target),
    axis: 'vertical',
  })

  const newItemList = reorder({
    list: targetParent.children,
    startIndex: sourceIdx,
    finishIndex: destinationIndex,
  })
  const updateRoot = updateItemListByPath(
    cloneDeep(treeRoot),
    targetParent.path,
    newItemList,
  )
  if (updateRoot) setTreeRoot(updateRoot)
}

const handleDifferentContainerDrop = (
  source: FormItemType,
  target: FormItemType,
  targetParent: FormItemType,
  treeRoot: FormItemType,
  setTreeRoot: (node: FormItemType) => void,
) => {
  const sourceParent = getItemByPath(treeRoot, source.path.slice(0, -1))
  if (!sourceParent) return

  const newRoot = cloneDeep(treeRoot)
  const delUpdateRoot = updateItemListByPath(
    newRoot,
    sourceParent.path,
    sourceParent.children.filter(i => i.formItemId !== source.formItemId),
  )
  if (!delUpdateRoot) return

  if (ContainerList.includes(target.type)) {
    const delTarget = getItemByPath(delUpdateRoot, target.path)
    if (!delTarget) return

    delTarget.children.push({
      ...source,
      path: [...delTarget.path, source.formItemId],
    })
    const newTreeRoot = updateItemListByPath(
      delUpdateRoot,
      delTarget.path,
      delTarget.children,
    )
    setTreeRoot(newTreeRoot)
  } else {
    const targetIdx = targetParent.children.findIndex(
      i => i.formItemId === target.formItemId,
    )
    const destinationIndex = getReorderDestinationIndex({
      startIndex: targetParent.children.length,
      indexOfTarget: targetIdx,
      closestEdgeOfTarget: extractClosestEdge(target),
      axis: 'vertical',
    })

    const newItemList = cloneDeep(
      getItemByPath(delUpdateRoot, targetParent.path)?.children,
    )
    if (!newItemList) return

    newItemList.splice(destinationIndex, 0, {
      ...source,
      path: [...targetParent.path, source.formItemId],
    })

    const updateRoot = updateItemListByPath(
      delUpdateRoot,
      targetParent.path,
      newItemList,
    )
    if (updateRoot) setTreeRoot(updateRoot)
  }
}

export const useDropComponents = (
  rootRef: React.RefObject<HTMLDivElement>,
  treeRoot: FormItemType,
  setTreeRoot: (node: FormItemType) => void,
) => {
  useEffect(() => {
    if (!rootRef.current) return

    return combine(
      dropTargetForElements({
        element: rootRef.current,
        getData: args => {
          const { input, element } = args
          const data = { ...treeRoot }
          return attachClosestEdge(data, { input, element, allowedEdges: [] })
        },
      }),
      monitorForElements({
        onDrop: args => {
          const {
            location,
            source: { data: source },
          } = args as {
            location: DragLocationHistory
            source: ElementDragPayload &
              (
                | { data: FormItemType }
                | { data: { type: 'selector'; itemType: FormSelectorType } }
              )
          }

          if (!location.current.dropTargets.length) return

          const [{ data: target }, dropTargetParent] = location.current
            .dropTargets as (DropTargetRecord & { data: FormItemType })[]
          const targetParent = dropTargetParent?.data

          if (source.type === 'selector') {
            handleAddItem(source, target, targetParent, treeRoot, setTreeRoot)
            return
          }

          if (target.type === 'root') {
            handleDropToRoot(source, target, treeRoot, setTreeRoot)
            return
          }

          const sourceParent = getItemByPath(treeRoot, source.path.slice(0, -1))
          if (!sourceParent || !targetParent) return

          const isSameContainer =
            source.path.slice(0, -1).join('-') ===
            target.path.slice(0, -1).join('-')

          if (isSameContainer) {
            handleSameContainerReorder(
              source,
              target,
              targetParent,
              treeRoot,
              setTreeRoot,
            )
          } else {
            handleDifferentContainerDrop(
              source,
              target,
              targetParent,
              treeRoot,
              setTreeRoot,
            )
          }
        },
      }),
    )
  }, [treeRoot])
}
