import { makeAutoObservable } from 'mobx'
import apiConfig from 'services/api'
import { createApiRequest, RequestData } from 'services/request'
import { AxiosResponse } from 'axios'

// Define the index structure according to API response
export interface Index {
  id: number
  agentID: number
  indexName: string
  description: string
  documentCount: number
  documentSize: number
  hitCount: number
  organizationID: number
  createdAt: string
  updatedAt: string
}

export interface FileDetails {
  createdAt: string
  filename: string
  id: number
  organizationID: number
  path: string
  size: number
  updatedAt: string
  userID: number
  username: string
}

export interface IndexDocument {
  id: number
  indexID: number
  organizationID: number
  fileID: number
  hitCount: number
  chunkSize: number
  source: string
  status: string
  createdAt: string
  updatedAt: string
  fileDetails: FileDetails
}

// Adjust the IndexList to handle index
interface IndexList {
  totalRecords: number
  indexes: Index[]
}
interface DocumentList {
  totalRecords: number
  documents: IndexDocument[]
}

class IndexStore {
  indexes: IndexList = {
    totalRecords: 0,
    indexes: [],
  }
  documents: DocumentList = {
    totalRecords: 0,
    documents: [],
  }

  constructor() {
    makeAutoObservable(this)
  }

  getList(
    agentID: number,
    page: number,
    size: number,
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      queryParams: {
        agentID,
        page,
        size,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexList, params)
        .then((response: AxiosResponse<any>) => {
          this.indexes = {
            totalRecords: response.data.totalCount,
            indexes: response.data.indexes,
          }
          resolve(response)
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error fetching documents'))
        })
    })
  }
  delete(id: number): Promise<void> {
    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexDelete, { queryParams: { id } })
        .then(() => {
          resolve()
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error deleting document'))
        })
    })
  }

  update(
    id: number,
    indexName: string,
    description: string,
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      body: {
        indexName,
        id,
        description,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexUpdate, params)
        .then(response => {
          const index = this.indexes.indexes.findIndex(index => index.id === id)
          if (index !== -1) {
            this.indexes.indexes[index].indexName = indexName
            this.indexes.indexes[index].description = description
          }
          resolve(response)
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error updating document'))
        })
    })
  }

  create(
    agentID: number,
    indexName: string,
    description: string,
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      body: {
        indexName,
        agentID,
        description,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexCreate, params)
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error creating document'))
        })
    })
  }

  getDocumentList(
    indexID: number,
    page: number,
    size: number,
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      queryParams: {
        indexID,
        page,
        size,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexMappingList, params)
        .then((response: AxiosResponse<any>) => {
          const mappings = response.data.mappings.map(
            (mapping: any): IndexDocument => ({
              id: mapping.id,
              indexID: mapping.indexID,
              organizationID: mapping.organizationID,
              fileID: mapping.fileID,
              hitCount: mapping.hitCount,
              chunkSize: mapping.chunkSize,
              source: mapping.source,
              status: mapping.status,
              createdAt: mapping.createdAt,
              updatedAt: mapping.updatedAt,
              fileDetails: {
                createdAt: mapping.file.createdAt,
                filename: mapping.file.filename,
                id: mapping.file.id,
                organizationID: mapping.file.organizationID,
                path: mapping.file.path,
                size: mapping.file.size,
                updatedAt: mapping.file.updatedAt,
                userID: mapping.file.userID,
                username: mapping.file.username,
              },
            }),
          )

          this.documents = {
            totalRecords: response.data.totalCount,
            documents: mappings,
          }
          resolve(response)
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error fetching documents'))
        })
    })
  }

  batchAddDocument(
    indexID: number,
    fileIDs: number[],
    chunkSize: number,
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      body: {
        indexID,
        fileIDs,
        chunkSize,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexMappingBatchAdd, params)
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error adding document'))
        })
    })
  }

  batchDeleteDocument(
    indexID: number,
    fileIDs: number[],
  ): Promise<AxiosResponse<any>> {
    const params: RequestData = {
      body: {
        indexID,
        fileIDs,
      },
    }

    return new Promise((resolve, reject) => {
      createApiRequest(apiConfig.indexMappingBatchDelete, params)
        .then(response => {
          resolve(response)
        })
        .catch(error => {
          reject(new Error(error.message ?? 'Error deleting document'))
        })
    })
  }
}

export default new IndexStore()
