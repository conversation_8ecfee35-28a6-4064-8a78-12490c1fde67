import { isDev } from 'utils/env'
import { create } from 'zustand'
import { devtools, persist, createJSONStorage } from 'zustand/middleware'

type GlobalState = {
  collapsed: boolean
  selectedMenu: string
  isDeveloperMode: boolean
  theme: 'dark' | 'light'

  toggleDeveloperMode: (v: boolean) => void
  setTheme: (theme: 'dark' | 'light') => void
}
export const useGlobalStore = create<GlobalState>()(
  persist(
    devtools(
      (set, get) => ({
        collapsed: false,
        selectedMenu: '',
        isDeveloperMode: true,
        theme: 'light',

        toggleDeveloperMode: (v: boolean) => {
          set({ isDeveloperMode: v })
        },

        setTheme: (theme: 'dark' | 'light') => {
          set({ theme })
        },
      }),
      {
        name: 'Workflow Store',
        enabled: isDev,
        trace: true,
      },
    ),
    {
      name: 'GlobalStore',
      storage: createJSONStorage(() => window.localStorage),
    },
  ),
)
