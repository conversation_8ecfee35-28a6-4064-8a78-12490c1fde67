import type RootNode from 'views/portal/agent/studio/new-workflow/nodes/root'
import type BaseNode from 'views/portal/agent/studio/new-workflow/lib/base-node'
import { NodeTypeEnum } from 'views/portal/agent/studio/new-workflow/lib/base-node/type'

export const getAllNodes = (
  node: RootNode | BaseNode | null,
): Record<string, BaseNode> => {
  if (!node) return {}
  const result: Record<string, BaseNode> = {}
  const traverseTree = (currentNode: BaseNode) => {
    Reflect.set(result, currentNode.node.data.id, currentNode)

    if (currentNode.type === NodeTypeEnum.Root) {
      ;(currentNode as RootNode).node.freeNodes.forEach(node =>
        traverseTree(node),
      )
    }

    if (
      currentNode.node.data.children &&
      currentNode.node.data.children.length > 0
    ) {
      currentNode.node.data.children.forEach((child: BaseNode) => {
        traverseTree(child)
      })
    }
  }

  traverseTree(node)
  return result
}
