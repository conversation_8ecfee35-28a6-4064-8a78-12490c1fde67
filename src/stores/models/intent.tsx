import { OutputField as Field } from 'views/portal/agent/studio/workflow/model'

export interface Intent {
  id: number
  organizationID: number
  intent: string
  description: string
  examples: IntentExample[]
  parameters: IntentParameter[]
}

export type IntentParameter = Field

export interface IntentExample {
  example: string
  parameters: string[]
}

export interface IntentList {
  totalCount: number
  intents: Intent[]
}
