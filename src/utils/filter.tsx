import { GLOBAL_DATE_TIME_FORMAT } from 'constants/common'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { isEmpty } from './common'

dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * locale info like global_date_time_format should be stored in global store
 */

export const dayjsUTC = dayjs

/**
 * Get the current timezone offset in hours
 */
export const currentTimezone = () => dayjsUTC().utcOffset() / 60

export const formatDate = (
  text: string,
  format = GLOBAL_DATE_TIME_FORMAT,
): string => {
  if (isEmpty(text)) return ''
  return dayjs(text).format(format)
}

export const formatFileSize = (
  sizeInBytes: number,
  withSpace: boolean = false,
): string => {
  const space = withSpace ? ' ' : ''
  if (sizeInBytes === 0) return `0${space}Bytes`
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(sizeInBytes) / Math.log(k))
  return (
    parseFloat((sizeInBytes / Math.pow(k, i)).toFixed(2)) + space + sizes[i]
  )
}
