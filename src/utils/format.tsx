import invariant from './invariant'

export const toLowerFirstLetter = (str: string): string => {
  invariant(typeof str === 'string', 'argumment is not a string')
  return str.charAt(0).toLowerCase() + str.slice(1)
}

export const capitalizeFirstLetter = (str: string): string => {
  invariant(typeof str === 'string', 'argumment is not a string')
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export const camelToWords = (camelCaseString: string): string => {
  return camelCaseString
    .replace(/([A-Z])/g, ' $1')
    .replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

/**
 * Convert a snake_case string to a title case string
 * @param input - 'snake_case'
 * @returns 'Snake Case'
 */
export function snakeCaseToWords(input: string): string {
  return input
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}
