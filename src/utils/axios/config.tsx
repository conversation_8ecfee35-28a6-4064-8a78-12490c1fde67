import { AxiosError } from 'axios'
import StaticFunction from 'components/antd-static-function'

import {
  HttpStatusCode,
  HttpStatusCodeManager,
} from 'constants/http-status-codes'

import { useGlobalStore } from 'stores/global'
import userStore from 'stores/user'
import { ApiErrorCodes } from './api-error-codes'
import { getApiErrorCode, getApiErrorMessage } from './api-error-message'
import { RequestConfig } from './type'

const TIME_OUT: number = 10 * 1000

const GLOBAL_INTERCEPTORS = {
  REQUEST_INTERCEPTOR: (config: any) => {
    return config
  },

  REQUEST_INTERCEPTOR_CATCH: (err: any) => {
    return Promise.reject(err instanceof Error ? err : new Error(String(err)))
  },

  RESPONSE_INTERCEPTOR: (res: any) => {
    return res
  },

  RESPONSE_INTERCEPTOR_CATCH: (err: any) => {
    return Promise.reject(err instanceof Error ? err : new Error(String(err)))
  },
}

const INSTANCE_INTERCEPTORS = {
  requestInterceptor: (config: any) => {
    return config
  },

  requestInterceptorCatch: (err: any) => {
    return Promise.reject(err instanceof Error ? err : new Error(String(err)))
  },

  responseInterceptor: (res: any) => {
    return res
  },

  responseInterceptorCatch: (err: AxiosError<any>) => {
    const { notification } = StaticFunction
    const { status, response, config } = err
    const { data } = response ?? {}
    const { interceptors } = (config as RequestConfig) ?? {}

    if (
      status === HttpStatusCode.UNAUTHORIZED &&
      (data?.code === ApiErrorCodes.AuthenticationRequired ||
        data?.code === ApiErrorCodes.InvalidToken)
    ) {
      if (location.pathname !== '/login') {
        userStore.setUser(null)
        location.pathname = '/login'
      }
    }

    /*
      if interceptors?.responseInterceptorCatch is not provided, it will use INSTANCE_INTERCEPTORS.responseInterceptorCatch
      therefore we need to check if interceptors?.responseInterceptorCatch !== INSTANCE_INTERCEPTORS.responseInterceptorCatch to
      determine if override interceptor is provided from argument
     */
    if (
      status !== HttpStatusCode.OK &&
      interceptors?.responseInterceptorCatch !==
        INSTANCE_INTERCEPTORS.responseInterceptorCatch
    ) {
      return interceptors?.responseInterceptorCatch?.(err)
    }

    if (status !== HttpStatusCode.OK) {
      // when response does not contain information required to determine exact error,
      // use http code to determine error message
      const fallbackHttpCodeErrorDetails = HttpStatusCodeManager.getDetails(
        Number(status),
      )
      notification.error({
        message: `Error: ${getApiErrorCode(data) ?? fallbackHttpCodeErrorDetails.code}`,
        description: useGlobalStore.getState().isDeveloperMode ? (
          <>
            <div>URL: {err.config?.url}</div>
            <div>
              {data?.detail ??
                data?.message ??
                fallbackHttpCodeErrorDetails.message}
            </div>
          </>
        ) : (
          <div>
            {getApiErrorMessage(data) ?? fallbackHttpCodeErrorDetails.message}
          </div>
        ),
      })
    }

    return Promise.reject(err)
  },
}

export { GLOBAL_INTERCEPTORS, INSTANCE_INTERCEPTORS, TIME_OUT }
