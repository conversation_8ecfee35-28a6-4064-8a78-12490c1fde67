/**
 * Error codes enum for use in the application
 */
export enum ApiErrorCodes {
  // General errors (100xxx)
  InvalidInput = 100000,
  DatabaseException = 100001,
  FailedToProcessDocument = 100002,
  // InternalServer = 100002,
  AuthenticationRequired = 100003,
  InvalidToken = 100004,
  FetchingFileFromRequest = 100005,
  FileValidationFailed = 100006,
  SavingUploadedFile = 100007,
  PermissionDenied = 100008,
  RecordNotFound = 100009,

  // Global errors (200xxx)
  OrganizationNotFound = 200000,
  OrganizationExpired = 200001,
  OrganizationDisabled = 200002,
  InvalidCredentials = 200003,
  UserDisabled = 200004,
  TwoFactorOTPRequired = 200005,
  UserChangePasswordRequired = 200006,
  UserSetup2FARequired = 200007,
  TwoFactorAuthenticationFailed = 200008,

  // User module errors (300xxx)
  AgentNotFound = 300000,
  IndexNotFound = 300001,
  PublishChannelNotFound = 300002,
  WorkflowNotFound = 300003,

  // File module errors (400xxx)
  FileNotFound = 400001,
  UpdateFilenameFailed = 400002,
  DeleteFileFailed = 400003,
  FetchFilesFailed = 400004,

  // User management errors (500xxx)
  UserAlreadyExists = 500001,
  UserNotFound = 500002,
  SessionExpired = 500003,
  Invalid2FACode = 500004,
  UserNotEligible = 500005,

  // Question/Metrics errors (600xxx)
  QuestionNotFound = 600000,
  MetricsNotFound = 600001,
  RoleNotFound = 600002,

  // API Key errors (700xxx)
  APIKeyNotFound = 700000,
  CreateAPIKeyFailed = 700001,
  DeleteAPIKeyFailed = 700002,
  FetchAPIKeysFailed = 700003,
  InvalidAPIKey = 700004,
  UpdateAPIKeyFailed = 700005,
}
