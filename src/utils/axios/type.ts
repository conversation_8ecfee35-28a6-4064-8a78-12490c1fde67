import {
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios'

// Instance
export interface RequestInterceptors<T = AxiosResponse> {
  requestInterceptor?: (
    config: AxiosRequestConfig,
  ) => InternalAxiosRequestConfig
  requestInterceptorCatch?: (err: any) => any
  responseInterceptor?: (config: AxiosResponse<T>) => AxiosResponse<T>
  // !!Need to return Promise.reject(err) to stop the request if provided in request params
  responseInterceptorCatch?: (err: any) => any
}

export interface RequestConfig<T = AxiosResponse> extends AxiosRequestConfig {
  interceptors?: RequestInterceptors<T>
  spinLoading?: boolean
}
