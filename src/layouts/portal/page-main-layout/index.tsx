import { LeftOutlined } from '@ant-design/icons'
import { Avatar, Button, ButtonProps, Flex, Radio, RadioGroupProps } from 'antd'
import cls from 'classnames'
import React, { useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { MainLayoutContext } from './context'
import styles from './index.scss'

export interface MenuItemProps {
  text: string
  icon?: React.ReactNode
  value: any
  default?: boolean
}

export interface ActionItemProps extends ButtonProps {
  text: string
  isPrimary?: boolean
}

export interface MainLayoutProps {
  title?: React.ReactNode
  caption?: React.ReactNode
  icon?: React.ReactNode
  onClickBack?: () => void
  onClickMenuItem?: (value: any) => void
  menu?: MenuItemProps[]
  actions?: ActionItemProps[]
  children?: React.ReactNode
  noBack?: boolean
  noHeaderBottomLine?: boolean
}

export type MainLayoutPropsType = {
  setMenu: (value: MenuItemProps[]) => void
}

const PageMainLayout: React.FC<MainLayoutProps> = props => {
  const {
    title: initialTitle,
    icon: initialIcon,
    caption: initialCaption,
    menu: initialMenu = [],
    actions: initialActions = [],
    onClickBack,
    onClickMenuItem,
    children,
    noBack,
    noHeaderBottomLine,
  } = props

  const navigate = useNavigate()
  const [menu, setMenu] = useState<MenuItemProps[]>([])
  const [caption, setCaption] = useState<React.ReactNode>()
  const [title, setTitle] = useState<React.ReactNode>()
  const [icon, setIcon] = useState<React.ReactNode>()
  const [actions, setActions] = useState<ActionItemProps[]>()
  const [registerEvents, setRegisterEvents] = useState<Function[]>([])

  const [curMenuItem, setCurMenuItem] = useState<string>('')

  const handleMenuItemClick: RadioGroupProps['onChange'] = e => {
    const path = e.target.value
    setCurMenuItem(path)
    navigate({ pathname: path, search: location.search })
    onClickMenuItem?.(path)
  }

  useEffect(() => {
    setMenu(initialMenu)
    setCurMenuItem(
      initialMenu.find(item => item.value === location.pathname)?.value ??
        initialMenu[0]?.value,
    )
    setCaption(initialCaption)
    setTitle(initialTitle)
    setIcon(initialIcon)
    setActions(initialActions)
  }, [])

  useEffect(() => {
    if (registerEvents.length) {
      registerEvents.forEach(fn => fn())
    }
  }, [registerEvents])

  const contextValue = useMemo(
    () => ({
      setMenu,
      setCaption,
      setTitle,
      setIcon,
      setActions,
      setRegisterEvents,
    }),
    [setMenu, setCaption, setTitle, setIcon, setActions, setRegisterEvents],
  )

  return (
    <Flex className={styles.mainLayoutWrapper} vertical>
      <Flex
        className={cls({
          [styles.mainLayoutWrapperHeader]: true,
          [styles.mainLayoutWrapperHeaderNoBack]: noBack,
          [styles.mainLayoutWrapperHeaderNoHeaderBottomLine]:
            noHeaderBottomLine,
        })}
        justify="space-between"
      >
        <Flex
          className={styles.mainLayoutWrapperHeaderLeftBlock}
          align="center"
          gap={8}
        >
          <Button
            type="text"
            icon={<LeftOutlined />}
            onClick={onClickBack}
            style={{ padding: 0 }}
          />
          <Avatar
            className={styles.mainLayoutWrapperHeaderAvatar}
            shape="square"
            size={32}
            icon={icon}
          />

          <Flex className={styles.mainLayoutWrapperHeaderInfo} vertical>
            <div className={styles.mainLayoutWrapperHeaderTitle}>{title}</div>
            <div className={styles.mainLayoutWrapperHeaderCaption}>
              {caption}
            </div>
          </Flex>
        </Flex>

        <div className={styles.mainLayoutWrapperHeaderMenuWrapper}>
          <Radio.Group
            className={styles.mainLayoutWrapperHeaderMenu}
            value={curMenuItem}
            onChange={handleMenuItemClick}
            options={menu?.map(item => ({
              value: item.value,
              label: (
                <Flex align="center" gap={8}>
                  {item.icon}
                  <span>{item.text}</span>
                </Flex>
              ),
            }))}
          />
        </div>

        <Flex gap={8} className={styles.mainLayoutWrapperHeaderActions}>
          {actions?.map(item => (
            <Button
              key={item.text}
              type={item.type ?? (item.isPrimary ? 'primary' : 'default')}
              icon={item.icon}
              onClick={item.onClick}
              disabled={item.disabled}
            >
              {item.text}
            </Button>
          ))}
        </Flex>
      </Flex>

      <MainLayoutContext.Provider value={contextValue}>
        {children}
      </MainLayoutContext.Provider>
    </Flex>
  )
}

export default PageMainLayout
