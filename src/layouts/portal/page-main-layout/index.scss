$large-screen: 1388px;
$medium-screen: 1000px;
$small-screen: 612px;

.main-layout-wrapper {
  height: 100%;

  &-header {
    padding: 24px 16px;
    border-bottom: 1px solid var(--ant-color-border);
    display: flex;
    gap: 8px;

    :global {
      @media screen and (max-width: $small-screen) {
        & {
          flex-direction: column;
        }
      }
    }

    &-left-block {
      flex: 1;
      min-width: 182px;

      :global {
        @media screen and (max-width: $small-screen) {
          & {
            justify-content: center;
          }
        }
      }
    }

    &-avatar {
      background-color: var(--ant-color-bg-container);
      border: none;
      flex-shrink: 0;

      & > * {
        width: 24px;
        height: 24px;
        color: #2a2c2f;
        justify-content: center;
      }
    }

    &-info {
      overflow: hidden;
      min-width: 72px;
    }

    &-title {
      color: var(--ant-color-text);
      font-size: 18px;
      font-weight: 700;
      line-height: normal;
    }

    &-caption {
      color: var(--ant-color-text-secondary);
      font-size: 12px;
      line-height: normal;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    &-actions {
      justify-content: end;
      align-self: center;
      flex-flow: wrap;
      flex: 1;

      &:empty {
        display: unset;
      }

      :global {
        @media screen and (max-width: $large-screen) {
          & {
            flex-wrap: wrap;
          }
        }

        @media screen and (max-width: $medium-screen) {
          & {
            width: 33.3%;
          }
        }

        @media screen and (max-width: $small-screen) {
          & {
            width: 67%;
          }
        }
      }
    }

    &-no-back {
      & > *:first-child {
        // Hidden the back button and avatar
        > :not(:last-child) {
          display: none;
        }

        // Show the title with larger font size
        > :last-child {
          > :first-child {
            font-size: 32px;
          }
        }
      }
    }

    &-no-header-bottom-line {
      border-bottom: none;
    }

    &-menu {
      display: flex;
      align-items: center;
      justify-content: center;

      &-wrapper {
        flex: 1;

        display: flex;
        align-items: center;
        justify-content: center;
      }

      :global {
        @media screen and (max-width: $medium-screen) {
          & {
            flex-direction: column;
          }
        }

        .ant-radio-wrapper {
          align-items: center;
          padding: 8px;
          margin: 0;
          justify-content: center;
          width: 100%;
        }

        .ant-radio-wrapper .ant-radio {
          display: none;
        }

        .ant-radio-wrapper.ant-radio-wrapper-checked {
          color: var(--ant-color-primary);
          background-color: var(--genie-color-bg-container);
          border-radius: 8px;
          font-weight: 700;
        }
      }
    }
  }
}
