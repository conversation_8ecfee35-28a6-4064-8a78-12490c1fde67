import { ActionItemProps } from 'layouts/portal/page-main-layout'
import { useCallback, useState } from 'react'
import { useLocation } from 'react-router-dom'

export interface RouteStates<TData = any> {
  data: TData
  caption: string
  pathname: string
  currentRoute: string
  actionItems: ActionItemProps[]
}

export interface OutletContext<TData = any> {
  routeStates: RouteStates<TData>
  setBreadcrumbCaption: (caption: string) => void
  setHeaderActionItems: (items: ActionItemProps[]) => void
}

/**
 * A generic hook for creating outlet context values that can be used with MainLayout
 * @param initialData Initial data to be stored in the route states
 * @returns An outlet context object with routeStates and methods to update them
 */
const useOutletContextValue = <TData = any>(
  initialData: TData,
): OutletContext<TData> => {
  const location = useLocation()

  const [routeStates, setRouteStates] = useState<RouteStates<TData>>({
    data: initialData,
    currentRoute: location.pathname,
    caption: '',
    pathname: location.pathname,
    actionItems: [],
  })

  const setBreadcrumbCaption = useCallback(
    (caption: string) => {
      setRouteStates(prev => ({
        ...prev,
        caption,
      }))
    },
    [setRouteStates],
  )

  const setHeaderActionItems = useCallback(
    (items: ActionItemProps[]) => {
      setRouteStates(prev => ({
        ...prev,
        actionItems: items,
      }))
    },
    [setRouteStates],
  )

  return {
    routeStates,
    setBreadcrumbCaption,
    setHeaderActionItems,
  }
}

export default useOutletContextValue
