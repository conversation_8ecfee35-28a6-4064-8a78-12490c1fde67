import { useEffect } from 'react'
import MessageWorkerBase, {
  MessageWorkerPayloadType,
} from '../utils/worker/message'

class MessageWorker extends MessageWorkerBase {
  constructor() {
    super()
  }

  postLifecycleMessage = (
    message: MessageWorkerPayloadType,
    targetOrigin = '*',
    targetWindow = this.targetWindow,
  ) => {
    this.postMessage(
      { msgType: 'lifecycle', payload: message },
      targetOrigin,
      targetWindow,
    )
  }

  postActionMessage = (
    message: MessageWorkerPayloadType,
    targetOrigin = '*',
    targetWindow = this.targetWindow,
  ) => {
    this.postMessage(
      { msgType: 'action', payload: message },
      targetOrigin,
      targetWindow,
    )
  }

  postNormalMessage = (
    message: MessageWorkerPayloadType,
    targetOrigin = '*',
    targetWindow = this.targetWindow,
  ) => {
    targetWindow?.postMessage(
      { msgType: 'message', payload: message },
      { targetOrigin },
    )
  }
}

/**
 *
 * @param onMessage
 * @returns messageWorker
 */
export const useMessageWorker = (onMessage?: (e: MessageEvent) => any) => {
  const messageWorker = new MessageWorker()

  useEffect(() => {
    messageWorker.register(onMessage)
    return () => messageWorker.remove(onMessage)
  }, [])

  return messageWorker
}
