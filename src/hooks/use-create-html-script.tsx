import { useEffect } from 'react'

type ElementType = 'style' | 'script'
/**
 *
 * @param type element type
 * @param textContent content
 */
export const useCreateHtmlScript = (
  type: ElementType,
  textContent?: string,
) => {
  useEffect(() => {
    if (!textContent) return
    const element = document.createElement(type)
    element.textContent = textContent
    document.head.appendChild(element)

    return () => {
      document.head.removeChild(element)
    }
  }, [textContent])
}
