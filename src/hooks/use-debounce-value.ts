import { useEffect, useState } from 'react'

export function useDebounceValue<T>(value: T, delay: number = 500) {
  const [debouncedValue, setDebouncedValue] = useState(value)
  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(timeout)
    }
  }, [value, delay])
  return debouncedValue
}

export default useDebounceValue
